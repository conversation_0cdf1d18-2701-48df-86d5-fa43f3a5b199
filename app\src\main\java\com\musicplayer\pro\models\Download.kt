package com.musicplayer.pro.models

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.util.*

/**
 * نموذج التحميل - مطابق لـ Download في Python
 */
@Parcelize
data class Download(
    val id: String = UUID.randomUUID().toString(),
    val title: String,
    val artist: String = "",
    val url: String,
    val thumbnailUrl: String = "",
    val duration: Int = 0, // بالثواني
    val fileSize: Long = 0, // بالبايت
    val quality: String = "128kbps",
    val format: String = "mp3",
    val status: DownloadStatus = DownloadStatus.PENDING,
    val progress: Int = 0, // 0-100
    val downloadSpeed: String = "",
    val eta: String = "", // Estimated Time of Arrival
    val filePath: String = "",
    val createdAt: Long = System.currentTimeMillis(),
    val completedAt: Long? = null,
    val errorMessage: String? = null,
    // خصائص إضافية للتحميل
    val totalSize: Long = 0,
    val downloadedSize: Long = 0,
    val speed: String = "",
    val dateCompleted: Long? = null
) : Parcelable {
    
    /**
     * الحصول على حجم الملف المنسق
     */
    fun getFormattedFileSize(): String {
        return when {
            fileSize < 1024 -> "$fileSize B"
            fileSize < 1024 * 1024 -> "${fileSize / 1024} KB"
            fileSize < 1024 * 1024 * 1024 -> "${fileSize / (1024 * 1024)} MB"
            else -> "${fileSize / (1024 * 1024 * 1024)} GB"
        }
    }
    
    /**
     * الحصول على المدة المنسقة
     */
    fun getFormattedDuration(): String {
        val minutes = duration / 60
        val seconds = duration % 60
        return String.format("%d:%02d", minutes, seconds)
    }
    
    /**
     * فحص إذا كان التحميل مكتملاً
     */
    fun isCompleted(): Boolean {
        return status == DownloadStatus.COMPLETED
    }
    
    /**
     * فحص إذا كان التحميل قيد التنفيذ
     */
    fun isActive(): Boolean {
        return status == DownloadStatus.DOWNLOADING || status == DownloadStatus.PENDING
    }
    
    /**
     * فحص إذا كان التحميل فاشلاً
     */
    fun isFailed(): Boolean {
        return status == DownloadStatus.FAILED
    }
    
    /**
     * فحص إذا كان التحميل متوقفاً
     */
    fun isPaused(): Boolean {
        return status == DownloadStatus.PAUSED
    }
    
    /**
     * فحص إذا كان التحميل ملغياً
     */
    fun isCancelled(): Boolean {
        return status == DownloadStatus.CANCELLED
    }

    /**
     * الحصول على نسبة التقدم
     */
    fun getProgressPercentage(): Int {
        return progress
    }

    /**
     * الحصول على الحجم المحمل المنسق
     */
    fun getFormattedDownloadedSize(): String {
        return formatFileSize(downloadedSize)
    }

    /**
     * الحصول على الحجم الإجمالي المنسق
     */
    fun getFormattedTotalSize(): String {
        return formatFileSize(totalSize)
    }

    /**
     * تنسيق حجم الملف
     */
    private fun formatFileSize(size: Long): String {
        return when {
            size < 1024 -> "$size B"
            size < 1024 * 1024 -> "${size / 1024} KB"
            size < 1024 * 1024 * 1024 -> "${size / (1024 * 1024)} MB"
            else -> "${size / (1024 * 1024 * 1024)} GB"
        }
    }
}
