#!/bin/bash

# Music Player Pro - Kotlin Setup Script for Linux/Mac
# سكريبت إعداد مشروع Music Player Pro لـ Linux/Mac

set -e  # إيقاف عند أول خطأ

# ألوان للطباعة
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# دوال مساعدة
print_header() {
    echo -e "\n${BLUE}========================================${NC}"
    echo -e "${BLUE}🚀 $1${NC}"
    echo -e "${BLUE}========================================${NC}\n"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${YELLOW}📝 $1${NC}"
}

# فحص نظام التشغيل
detect_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        OS="linux"
        DISTRO=$(lsb_release -si 2>/dev/null || echo "Unknown")
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="mac"
        DISTRO="macOS"
    else
        print_error "نظام التشغيل غير مدعوم: $OSTYPE"
        exit 1
    fi
    
    print_info "نظام التشغيل: $OS ($DISTRO)"
}

# فحص وتثبيت المتطلبات الأساسية
install_prerequisites() {
    print_header "تثبيت المتطلبات الأساسية"
    
    # فحص Python
    if ! command -v python3 &> /dev/null; then
        print_info "تثبيت Python 3..."
        if [[ "$OS" == "linux" ]]; then
            sudo apt update
            sudo apt install -y python3 python3-pip
        elif [[ "$OS" == "mac" ]]; then
            if command -v brew &> /dev/null; then
                brew install python3
            else
                print_error "يرجى تثبيت Homebrew أولاً: https://brew.sh"
                exit 1
            fi
        fi
    fi
    print_success "Python 3 موجود"
    
    # فحص Git
    if ! command -v git &> /dev/null; then
        print_info "تثبيت Git..."
        if [[ "$OS" == "linux" ]]; then
            sudo apt install -y git
        elif [[ "$OS" == "mac" ]]; then
            if command -v brew &> /dev/null; then
                brew install git
            else
                xcode-select --install
            fi
        fi
    fi
    print_success "Git موجود"
    
    # فحص curl و unzip
    if [[ "$OS" == "linux" ]]; then
        sudo apt install -y curl unzip wget
    fi
}

# تثبيت Java
install_java() {
    print_header "إعداد Java"
    
    if ! command -v java &> /dev/null; then
        print_info "تثبيت OpenJDK 11..."
        
        if [[ "$OS" == "linux" ]]; then
            sudo apt update
            sudo apt install -y openjdk-11-jdk
        elif [[ "$OS" == "mac" ]]; then
            if command -v brew &> /dev/null; then
                brew install openjdk@11
                # ربط Java
                sudo ln -sfn $(brew --prefix)/opt/openjdk@11/libexec/openjdk.jdk /Library/Java/JavaVirtualMachines/openjdk-11.jdk
            fi
        fi
        
        # إعداد JAVA_HOME
        if [[ "$OS" == "linux" ]]; then
            export JAVA_HOME="/usr/lib/jvm/java-11-openjdk-amd64"
        elif [[ "$OS" == "mac" ]]; then
            export JAVA_HOME="$(/usr/libexec/java_home -v 11)"
        fi
        
        echo "export JAVA_HOME=$JAVA_HOME" >> ~/.bashrc
        echo "export PATH=\$JAVA_HOME/bin:\$PATH" >> ~/.bashrc
        
    else
        print_success "Java موجود"
        export JAVA_HOME=$(dirname $(dirname $(readlink -f $(which java))))
    fi
    
    print_success "تم إعداد Java: $JAVA_HOME"
}

# تحميل وإعداد Android SDK
setup_android_sdk() {
    print_header "إعداد Android SDK"
    
    # إنشاء مجلدات
    mkdir -p tools android-sdk
    
    # تحديد رابط التحميل حسب نظام التشغيل
    if [[ "$OS" == "linux" ]]; then
        SDK_URL="https://dl.google.com/android/repository/commandlinetools-linux-9477386_latest.zip"
    elif [[ "$OS" == "mac" ]]; then
        SDK_URL="https://dl.google.com/android/repository/commandlinetools-mac-9477386_latest.zip"
    fi
    
    # تحميل Command Line Tools
    if [[ ! -f "tools/android-sdk-tools.zip" ]]; then
        print_info "تحميل Android Command Line Tools..."
        curl -L "$SDK_URL" -o tools/android-sdk-tools.zip
    fi
    
    # استخراج SDK
    if [[ ! -d "android-sdk/cmdline-tools/latest" ]]; then
        print_info "استخراج Android SDK..."
        unzip -q tools/android-sdk-tools.zip -d android-sdk/cmdline-tools/
        mv android-sdk/cmdline-tools/cmdline-tools android-sdk/cmdline-tools/latest
    fi
    
    # إعداد متغيرات البيئة
    export ANDROID_HOME="$(pwd)/android-sdk"
    export ANDROID_SDK_ROOT="$ANDROID_HOME"
    export PATH="$ANDROID_HOME/cmdline-tools/latest/bin:$ANDROID_HOME/platform-tools:$PATH"
    
    # إضافة إلى bashrc
    echo "export ANDROID_HOME=$ANDROID_HOME" >> ~/.bashrc
    echo "export ANDROID_SDK_ROOT=$ANDROID_SDK_ROOT" >> ~/.bashrc
    echo "export PATH=\$ANDROID_HOME/cmdline-tools/latest/bin:\$ANDROID_HOME/platform-tools:\$PATH" >> ~/.bashrc
    
    # قبول التراخيص وتثبيت المكونات
    if [[ -f "$ANDROID_HOME/cmdline-tools/latest/bin/sdkmanager" ]]; then
        print_info "قبول تراخيص Android SDK..."
        yes | $ANDROID_HOME/cmdline-tools/latest/bin/sdkmanager --licenses > /dev/null 2>&1
        
        print_info "تثبيت مكونات Android SDK..."
        $ANDROID_HOME/cmdline-tools/latest/bin/sdkmanager "platform-tools" > /dev/null
        $ANDROID_HOME/cmdline-tools/latest/bin/sdkmanager "platforms;android-34" > /dev/null
        $ANDROID_HOME/cmdline-tools/latest/bin/sdkmanager "platforms;android-33" > /dev/null
        $ANDROID_HOME/cmdline-tools/latest/bin/sdkmanager "build-tools;34.0.0" > /dev/null
        $ANDROID_HOME/cmdline-tools/latest/bin/sdkmanager "build-tools;33.0.0" > /dev/null
        $ANDROID_HOME/cmdline-tools/latest/bin/sdkmanager "ndk;25.2.9519653" > /dev/null
    fi
    
    print_success "تم إعداد Android SDK"
}

# تحميل وإعداد Gradle
setup_gradle() {
    print_header "إعداد Gradle"
    
    # تحميل Gradle
    if [[ ! -f "tools/gradle.zip" ]]; then
        print_info "تحميل Gradle 8.4..."
        curl -L "https://services.gradle.org/distributions/gradle-8.4-bin.zip" -o tools/gradle.zip
    fi
    
    # استخراج Gradle
    if [[ ! -d "tools/gradle/gradle-8.4" ]]; then
        print_info "استخراج Gradle..."
        unzip -q tools/gradle.zip -d tools/gradle/
    fi
    
    # إضافة Gradle إلى PATH
    export PATH="$(pwd)/tools/gradle/gradle-8.4/bin:$PATH"
    echo "export PATH=$(pwd)/tools/gradle/gradle-8.4/bin:\$PATH" >> ~/.bashrc
    
    print_success "تم إعداد Gradle"
}

# إنشاء ملفات المشروع
create_project_files() {
    print_header "إنشاء ملفات المشروع"
    
    # إنشاء مجلدات
    mkdir -p app/src/main/{java/com/musicplayer/pro,res/{layout,values,values-ar,drawable,mipmap-{h,m,x,xx,xxx}dpi}}
    
    # إنشاء gradle.properties
    if [[ ! -f "gradle.properties" ]]; then
        cat > gradle.properties << 'EOF'
org.gradle.jvmargs=-Xmx4g -Dfile.encoding=UTF-8
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.configureondemand=true

android.useAndroidX=true
android.enableJetifier=true
android.nonTransitiveRClass=true

kotlin.code.style=official
kotlin.incremental=true
kotlin.incremental.android=true
EOF
    fi
    
    # إنشاء settings.gradle
    if [[ ! -f "settings.gradle" ]]; then
        cat > settings.gradle << 'EOF'
pluginManagement {
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}

dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        maven { url 'https://jitpack.io' }
    }
}

rootProject.name = "MusicPlayerPro"
include ':app'
EOF
    fi
    
    # إنشاء build.gradle الجذر
    if [[ ! -f "build.gradle" ]]; then
        cat > build.gradle << 'EOF'
buildscript {
    ext.kotlin_version = "1.9.21"
    dependencies {
        classpath 'com.android.tools.build:gradle:8.2.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

plugins {
    id 'com.android.application' version '8.2.0' apply false
    id 'org.jetbrains.kotlin.android' version '1.9.21' apply false
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
EOF
    fi
    
    # إنشاء local.properties
    cat > local.properties << EOF
sdk.dir=$ANDROID_HOME
ndk.dir=$ANDROID_HOME/ndk/25.2.9519653
EOF
    
    print_success "تم إنشاء ملفات المشروع"
}

# بناء المشروع
build_project() {
    print_header "بناء المشروع"
    
    # إنشاء Gradle Wrapper
    if [[ ! -f "gradlew" ]]; then
        print_info "إنشاء Gradle Wrapper..."
        gradle wrapper --gradle-version 8.4
    fi
    
    # جعل gradlew قابل للتنفيذ
    chmod +x gradlew
    
    # تنظيف المشروع
    print_info "تنظيف المشروع..."
    ./gradlew clean
    
    # بناء المشروع
    print_info "بناء المشروع... (قد يستغرق عدة دقائق)"
    if ./gradlew assembleDebug; then
        print_success "تم بناء المشروع بنجاح!"
        
        # البحث عن APK
        APK_PATH=$(find app/build/outputs/apk/debug -name "*.apk" 2>/dev/null | head -1)
        if [[ -n "$APK_PATH" ]]; then
            APK_SIZE=$(du -h "$APK_PATH" | cut -f1)
            print_success "تم إنشاء APK: $APK_PATH ($APK_SIZE)"
        fi
        
        return 0
    else
        print_error "فشل في بناء المشروع"
        return 1
    fi
}

# تشغيل سكريبت Python للإعداد المتقدم
run_python_setup() {
    print_header "تشغيل الإعداد المتقدم"
    
    if [[ -f "setup_and_run.py" ]]; then
        print_info "تشغيل سكريبت Python للإعداد المتقدم..."
        python3 setup_and_run.py
    else
        print_info "ملف setup_and_run.py غير موجود، تخطي الإعداد المتقدم"
    fi
}

# عرض معلومات ما بعد التثبيت
show_post_install_info() {
    print_header "معلومات ما بعد التثبيت"
    
    echo -e "${GREEN}✅ تم إعداد المشروع بنجاح!${NC}\n"
    
    echo -e "${YELLOW}📋 الأوامر المفيدة:${NC}"
    echo "  ./gradlew assembleDebug    - بناء APK للتطوير"
    echo "  ./gradlew assembleRelease  - بناء APK للإنتاج"
    echo "  ./gradlew clean           - تنظيف المشروع"
    echo "  ./gradlew build           - بناء كامل"
    echo ""
    
    echo -e "${YELLOW}📁 مجلدات مهمة:${NC}"
    echo "  $(pwd)                    - مجلد المشروع"
    echo "  app/build/outputs/apk/    - ملفات APK"
    echo "  android-sdk/              - Android SDK"
    echo "  tools/                    - الأدوات المحملة"
    echo ""
    
    echo -e "${YELLOW}🚀 الخطوات التالية:${NC}"
    echo "  1. يمكنك فتح المشروع في Android Studio"
    echo "  2. أو استخدام الأوامر أعلاه لبناء APK"
    echo "  3. تأكد من إعادة تحميل bashrc: source ~/.bashrc"
    echo ""
    
    if [[ -f "app/build/outputs/apk/debug/app-debug.apk" ]]; then
        echo -e "${GREEN}📱 APK جاهز للتثبيت!${NC}"
    fi
}

# الدالة الرئيسية
main() {
    print_header "Music Player Pro - Kotlin Setup"
    
    # فحص نظام التشغيل
    detect_os
    
    # تثبيت المتطلبات
    install_prerequisites
    install_java
    setup_android_sdk
    setup_gradle
    
    # إنشاء المشروع
    create_project_files
    
    # تشغيل الإعداد المتقدم
    run_python_setup
    
    # بناء المشروع
    if build_project; then
        show_post_install_info
    else
        print_error "فشل في إعداد المشروع"
        exit 1
    fi
}

# تشغيل الدالة الرئيسية
main "$@"
