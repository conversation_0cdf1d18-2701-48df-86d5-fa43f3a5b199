// Generated by view binder compiler. Do not edit!
package com.musicplayer.pro.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.Spinner;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import com.musicplayer.pro.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogAddDownloadBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final Button cancelButton;

  @NonNull
  public final TextView dialogTitle;

  @NonNull
  public final Button downloadButton;

  @NonNull
  public final TextView formatLabel;

  @NonNull
  public final Spinner formatSpinner;

  @NonNull
  public final TextView qualityLabel;

  @NonNull
  public final Spinner qualitySpinner;

  @NonNull
  public final TextInputEditText urlEditText;

  @NonNull
  public final TextInputLayout urlInputLayout;

  private DialogAddDownloadBinding(@NonNull ConstraintLayout rootView, @NonNull Button cancelButton,
      @NonNull TextView dialogTitle, @NonNull Button downloadButton, @NonNull TextView formatLabel,
      @NonNull Spinner formatSpinner, @NonNull TextView qualityLabel,
      @NonNull Spinner qualitySpinner, @NonNull TextInputEditText urlEditText,
      @NonNull TextInputLayout urlInputLayout) {
    this.rootView = rootView;
    this.cancelButton = cancelButton;
    this.dialogTitle = dialogTitle;
    this.downloadButton = downloadButton;
    this.formatLabel = formatLabel;
    this.formatSpinner = formatSpinner;
    this.qualityLabel = qualityLabel;
    this.qualitySpinner = qualitySpinner;
    this.urlEditText = urlEditText;
    this.urlInputLayout = urlInputLayout;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogAddDownloadBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogAddDownloadBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_add_download, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogAddDownloadBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.cancelButton;
      Button cancelButton = ViewBindings.findChildViewById(rootView, id);
      if (cancelButton == null) {
        break missingId;
      }

      id = R.id.dialogTitle;
      TextView dialogTitle = ViewBindings.findChildViewById(rootView, id);
      if (dialogTitle == null) {
        break missingId;
      }

      id = R.id.downloadButton;
      Button downloadButton = ViewBindings.findChildViewById(rootView, id);
      if (downloadButton == null) {
        break missingId;
      }

      id = R.id.formatLabel;
      TextView formatLabel = ViewBindings.findChildViewById(rootView, id);
      if (formatLabel == null) {
        break missingId;
      }

      id = R.id.formatSpinner;
      Spinner formatSpinner = ViewBindings.findChildViewById(rootView, id);
      if (formatSpinner == null) {
        break missingId;
      }

      id = R.id.qualityLabel;
      TextView qualityLabel = ViewBindings.findChildViewById(rootView, id);
      if (qualityLabel == null) {
        break missingId;
      }

      id = R.id.qualitySpinner;
      Spinner qualitySpinner = ViewBindings.findChildViewById(rootView, id);
      if (qualitySpinner == null) {
        break missingId;
      }

      id = R.id.urlEditText;
      TextInputEditText urlEditText = ViewBindings.findChildViewById(rootView, id);
      if (urlEditText == null) {
        break missingId;
      }

      id = R.id.urlInputLayout;
      TextInputLayout urlInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (urlInputLayout == null) {
        break missingId;
      }

      return new DialogAddDownloadBinding((ConstraintLayout) rootView, cancelButton, dialogTitle,
          downloadButton, formatLabel, formatSpinner, qualityLabel, qualitySpinner, urlEditText,
          urlInputLayout);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
