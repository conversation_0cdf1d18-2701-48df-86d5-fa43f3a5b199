package com.musicplayer.pro.services

import android.app.*
import android.content.Intent
import android.media.AudioAttributes
import android.media.AudioFocusRequest
import android.media.AudioManager
import android.media.MediaPlayer
import android.os.Binder
import android.os.Build
import android.os.IBinder
import android.os.Handler
import android.os.Looper
import android.os.PowerManager
import androidx.core.app.NotificationCompat
import com.musicplayer.pro.R
import com.musicplayer.pro.models.Song
import com.musicplayer.pro.models.RepeatMode
import java.io.IOException

/**
 * خدمة تشغيل الموسيقى - مطابقة لمنطق التشغيل في Python
 */
class MusicService : Service() {

    companion object {
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "music_player_channel"
        private const val UPDATE_INTERVAL = 1000L // تحديث كل ثانية
    }

    private val binder = MusicBinder()
    private var mediaPlayer: MediaPlayer? = null
    private var currentSong: Song? = null
    private var playlist: List<Song> = emptyList()
    private var currentIndex: Int = -1
    private var isShuffleEnabled = false
    private var repeatMode = RepeatMode.OFF

    // إدارة الصوت
    private lateinit var audioManager: AudioManager
    private var audioFocusRequest: AudioFocusRequest? = null
    private var hasAudioFocus = false

    // المستمعين
    private var onPlaybackStateChangedListener: ((Boolean) -> Unit)? = null
    private var onSongChangedListener: ((Song) -> Unit)? = null
    private var onPositionChangedListener: ((Int) -> Unit)? = null

    // Handler لتحديث الموضع
    private val handler = Handler(Looper.getMainLooper())
    private val updatePositionRunnable = object : Runnable {
        override fun run() {
            mediaPlayer?.let { player ->
                if (player.isPlaying) {
                    onPositionChangedListener?.invoke(player.currentPosition)
                }
            }
            handler.postDelayed(this, UPDATE_INTERVAL)
        }
    }

    inner class MusicBinder : Binder() {
        fun getService(): MusicService = this@MusicService
    }

    override fun onCreate() {
        super.onCreate()

        // تهيئة AudioManager
        audioManager = getSystemService(AUDIO_SERVICE) as AudioManager

        // تحسين إعدادات الصوت
        optimizeAudioSettings()

        createNotificationChannel()
        startPositionUpdates()
    }

    /**
     * تحسين إعدادات الصوت
     */
    private fun optimizeAudioSettings() {
        try {
            // تحسين إعدادات الصوت للتشغيل السلس
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                audioManager.setParameters("music_plus_stream_volume=1")
            }

            // تحسين معدل العينة
            val sampleRate = audioManager.getProperty(AudioManager.PROPERTY_OUTPUT_SAMPLE_RATE)
            val bufferSize = audioManager.getProperty(AudioManager.PROPERTY_OUTPUT_FRAMES_PER_BUFFER)

            android.util.Log.d("MusicService", "Sample Rate: $sampleRate, Buffer Size: $bufferSize")

        } catch (e: Exception) {
            android.util.Log.w("MusicService", "Could not optimize audio settings: ${e.message}")
        }
    }

    override fun onBind(intent: Intent?): IBinder = binder

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        return START_STICKY
    }

    /**
     * تشغيل أغنية
     */
    fun playSong(song: Song, newPlaylist: List<Song> = listOf(song)) {
        try {
            playlist = newPlaylist
            currentIndex = playlist.indexOf(song)
            currentSong = song

            // طلب Audio Focus قبل التشغيل
            if (!requestAudioFocus()) {
                return
            }

            // إيقاف التشغيل الحالي
            stopPlayback()

            // إنشاء MediaPlayer جديد مع إعدادات محسنة
            mediaPlayer = MediaPlayer().apply {
                // إعدادات الصوت المحسنة
                setAudioAttributes(
                    AudioAttributes.Builder()
                        .setUsage(AudioAttributes.USAGE_MEDIA)
                        .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
                        .build()
                )

                // إعدادات الطاقة لمنع النوم
                setWakeMode(applicationContext, PowerManager.PARTIAL_WAKE_LOCK)

                // إعدادات إضافية لتحسين الأداء
                try {
                    // تحسين حجم البافر للتشغيل السلس
                    val audioSessionId = audioSessionId
                    android.util.Log.d("MusicService", "Audio Session ID: $audioSessionId")
                } catch (e: Exception) {
                    android.util.Log.w("MusicService", "Could not get audio session ID: ${e.message}")
                }

                setDataSource(song.path)
                prepareAsync()

                setOnPreparedListener { player ->
                    player.start()
                    onPlaybackStateChangedListener?.invoke(true)
                    onSongChangedListener?.invoke(song)
                    showNotification()
                }

                setOnCompletionListener {
                    onSongCompleted()
                }

                setOnErrorListener { _, what, extra ->
                    android.util.Log.e("MusicService", "MediaPlayer error: what=$what, extra=$extra")
                    playNext()
                    true
                }

                setOnBufferingUpdateListener { _, percent ->
                    // يمكن إضافة معالجة التخزين المؤقت هنا
                    android.util.Log.d("MusicService", "Buffering: $percent%")
                }

                // إعدادات إضافية لتحسين الأداء
                setOnSeekCompleteListener {
                    android.util.Log.d("MusicService", "Seek completed")
                }

                setOnInfoListener { _, what, extra ->
                    when (what) {
                        MediaPlayer.MEDIA_INFO_BUFFERING_START -> {
                            android.util.Log.d("MusicService", "Buffering started")
                        }
                        MediaPlayer.MEDIA_INFO_BUFFERING_END -> {
                            android.util.Log.d("MusicService", "Buffering ended")
                        }
                    }
                    false
                }
            }

        } catch (e: Exception) {
            android.util.Log.e("MusicService", "خطأ في تشغيل الأغنية: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * تبديل التشغيل/الإيقاف
     */
    fun togglePlayPause() {
        mediaPlayer?.let { player ->
            if (player.isPlaying) {
                player.pause()
                onPlaybackStateChangedListener?.invoke(false)
            } else {
                // طلب Audio Focus قبل التشغيل
                if (requestAudioFocus()) {
                    player.start()
                    onPlaybackStateChangedListener?.invoke(true)
                }
            }
            showNotification()
        }
    }

    /**
     * تشغيل الأغنية التالية
     */
    fun playNext() {
        if (playlist.isNotEmpty()) {
            when (repeatMode) {
                RepeatMode.ONE -> {
                    // إعادة تشغيل نفس الأغنية
                    currentSong?.let { playSong(it, playlist) }
                }
                RepeatMode.ALL -> {
                    // الانتقال للأغنية التالية مع التكرار
                    currentIndex = if (currentIndex < playlist.size - 1) {
                        currentIndex + 1
                    } else {
                        0 // العودة للبداية
                    }
                    playSong(playlist[currentIndex], playlist)
                }
                RepeatMode.OFF -> {
                    // الانتقال للأغنية التالية فقط إذا لم نصل للنهاية
                    if (currentIndex < playlist.size - 1) {
                        currentIndex++
                        playSong(playlist[currentIndex], playlist)
                    } else {
                        stopPlayback()
                    }
                }
            }
        }
    }

    /**
     * تشغيل الأغنية السابقة
     */
    fun playPrevious() {
        if (playlist.isNotEmpty() && currentIndex > 0) {
            currentIndex--
            playSong(playlist[currentIndex], playlist)
        }
    }

    /**
     * البحث إلى موضع معين
     */
    fun seekTo(position: Int) {
        mediaPlayer?.seekTo(position)
    }

    /**
     * تعيين نمط التكرار
     */
    fun setRepeatMode(mode: RepeatMode) {
        repeatMode = mode
    }

    /**
     * تعيين التشغيل العشوائي
     */
    fun setShuffleEnabled(enabled: Boolean) {
        isShuffleEnabled = enabled
        if (enabled) {
            playlist = playlist.shuffled()
            currentIndex = playlist.indexOf(currentSong)
        }
    }

    /**
     * الحصول على الموضع الحالي
     */
    fun getCurrentPosition(): Int {
        return mediaPlayer?.currentPosition ?: 0
    }

    /**
     * الحصول على المدة الإجمالية
     */
    fun getDuration(): Int {
        return mediaPlayer?.duration ?: 0
    }

    /**
     * فحص حالة التشغيل
     */
    fun isPlaying(): Boolean {
        return mediaPlayer?.isPlaying ?: false
    }

    /**
     * الحصول على الأغنية الحالية
     */
    fun getCurrentSong(): Song? {
        return currentSong
    }

    /**
     * تعيين مستمع تغيير حالة التشغيل
     */
    fun setOnPlaybackStateChangedListener(listener: (Boolean) -> Unit) {
        onPlaybackStateChangedListener = listener
    }

    /**
     * تعيين مستمع تغيير الأغنية
     */
    fun setOnSongChangedListener(listener: (Song) -> Unit) {
        onSongChangedListener = listener
    }

    /**
     * تعيين مستمع تغيير الموضع
     */
    fun setOnPositionChangedListener(listener: (Int) -> Unit) {
        onPositionChangedListener = listener
    }

    /**
     * عند اكتمال الأغنية
     */
    private fun onSongCompleted() {
        when (repeatMode) {
            RepeatMode.ONE -> {
                // إعادة تشغيل نفس الأغنية
                mediaPlayer?.seekTo(0)
                mediaPlayer?.start()
            }
            else -> {
                playNext()
            }
        }
    }

    /**
     * إيقاف التشغيل
     */
    private fun stopPlayback() {
        mediaPlayer?.let { player ->
            if (player.isPlaying) {
                player.stop()
            }
            player.release()
        }
        mediaPlayer = null

        // تحرير Audio Focus
        abandonAudioFocus()

        onPlaybackStateChangedListener?.invoke(false)
    }

    /**
     * بدء تحديثات الموضع
     */
    private fun startPositionUpdates() {
        handler.post(updatePositionRunnable)
    }

    /**
     * إيقاف تحديثات الموضع
     */
    private fun stopPositionUpdates() {
        handler.removeCallbacks(updatePositionRunnable)
    }

    /**
     * إنشاء قناة الإشعارات
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "Music Player",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Music playback controls"
                setShowBadge(false)
            }

            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }

    /**
     * طلب Audio Focus
     */
    private fun requestAudioFocus(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val audioAttributes = AudioAttributes.Builder()
                .setUsage(AudioAttributes.USAGE_MEDIA)
                .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
                .build()

            audioFocusRequest = AudioFocusRequest.Builder(AudioManager.AUDIOFOCUS_GAIN)
                .setAudioAttributes(audioAttributes)
                .setAcceptsDelayedFocusGain(true)
                .setOnAudioFocusChangeListener(audioFocusChangeListener)
                .build()

            val result = audioManager.requestAudioFocus(audioFocusRequest!!)
            hasAudioFocus = result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED
            hasAudioFocus
        } else {
            @Suppress("DEPRECATION")
            val result = audioManager.requestAudioFocus(
                audioFocusChangeListener,
                AudioManager.STREAM_MUSIC,
                AudioManager.AUDIOFOCUS_GAIN
            )
            hasAudioFocus = result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED
            hasAudioFocus
        }
    }

    /**
     * تحرير Audio Focus
     */
    private fun abandonAudioFocus() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            audioFocusRequest?.let {
                audioManager.abandonAudioFocusRequest(it)
            }
        } else {
            @Suppress("DEPRECATION")
            audioManager.abandonAudioFocus(audioFocusChangeListener)
        }
        hasAudioFocus = false
    }

    /**
     * مستمع تغيير Audio Focus
     */
    private val audioFocusChangeListener = AudioManager.OnAudioFocusChangeListener { focusChange ->
        when (focusChange) {
            AudioManager.AUDIOFOCUS_GAIN -> {
                // استعادة التشغيل
                hasAudioFocus = true
                mediaPlayer?.let { player ->
                    if (!player.isPlaying) {
                        player.start()
                        onPlaybackStateChangedListener?.invoke(true)
                    }
                    player.setVolume(1.0f, 1.0f)
                }
            }
            AudioManager.AUDIOFOCUS_LOSS -> {
                // فقدان دائم للتركيز - إيقاف التشغيل
                hasAudioFocus = false
                mediaPlayer?.let { player ->
                    if (player.isPlaying) {
                        player.pause()
                        onPlaybackStateChangedListener?.invoke(false)
                    }
                }
            }
            AudioManager.AUDIOFOCUS_LOSS_TRANSIENT -> {
                // فقدان مؤقت - إيقاف مؤقت
                mediaPlayer?.let { player ->
                    if (player.isPlaying) {
                        player.pause()
                        onPlaybackStateChangedListener?.invoke(false)
                    }
                }
            }
            AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK -> {
                // خفض الصوت
                mediaPlayer?.setVolume(0.3f, 0.3f)
            }
        }
    }

    /**
     * عرض الإشعار
     */
    private fun showNotification() {
        val song = currentSong ?: return

        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(song.title)
            .setContentText(song.getArtistOrDefault())
            .setSmallIcon(R.drawable.ic_music_note)
            .setOngoing(true)
            .setShowWhen(false)
            .build()

        startForeground(NOTIFICATION_ID, notification)
    }

    /**
     * إخفاء الإشعار
     */
    private fun hideNotification() {
        stopForeground(true)
    }

    override fun onDestroy() {
        super.onDestroy()
        stopPlayback()
        stopPositionUpdates()
        abandonAudioFocus()
        hideNotification()
    }
}
