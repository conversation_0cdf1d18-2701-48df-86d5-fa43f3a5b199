android.app.Application(androidx.appcompat.app.AppCompatActivityandroidx.fragment.app.Fragmentkotlin.Enumandroid.os.Parcelable androidx.viewbinding.ViewBinding0androidx.viewpager2.adapter.FragmentStateAdapter1androidx.recyclerview.widget.RecyclerView.Adapter4androidx.recyclerview.widget.RecyclerView.ViewHolderandroid.app.Serviceandroid.os.Binder#androidx.lifecycle.AndroidViewModel,androidx.preference.PreferenceFragmentCompatBandroid.content.SharedPreferences.OnSharedPreferenceChangeListener                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           