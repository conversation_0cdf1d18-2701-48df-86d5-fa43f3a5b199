package com.musicplayer.pro.fragments

import android.app.AlertDialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.floatingactionbutton.FloatingActionButton
import com.google.android.material.textfield.TextInputEditText
import com.google.android.material.textfield.TextInputLayout
import com.musicplayer.pro.R
import com.musicplayer.pro.adapters.DownloadAdapter
import com.musicplayer.pro.models.Download
import com.musicplayer.pro.viewmodels.DownloadViewModel
import com.musicplayer.pro.utils.setOnTextChangedListener

/**
 * شاشة التحميلات
 * مطابقة لـ DownloadScreen في Python مع جميع الميزات
 */
class DownloadFragment : Fragment() {

    private lateinit var recyclerView: RecyclerView
    private lateinit var downloadAdapter: DownloadAdapter
    private lateinit var fabAddDownload: FloatingActionButton
    private lateinit var emptyStateLayout: LinearLayout
    private lateinit var viewModel: DownloadViewModel

    private var downloadDialog: AlertDialog? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_download, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // تهيئة ViewModel
        viewModel = ViewModelProvider(this)[DownloadViewModel::class.java]

        // تهيئة UI
        initializeUI(view)

        // إعداد المراقبين
        setupObservers()

        // ربط MusicService مع ViewModel
        bindMusicServiceToViewModel()

        // تحميل التحميلات
        loadDownloads()
    }

    /**
     * تهيئة واجهة المستخدم
     */
    private fun initializeUI(view: View) {
        recyclerView = view.findViewById(R.id.recyclerViewDownloads)
        fabAddDownload = view.findViewById(R.id.fabAddDownload)
        emptyStateLayout = view.findViewById(R.id.emptyStateLayout)

        setupRecyclerView()
        setupFab()
    }

    /**
     * إعداد RecyclerView
     */
    private fun setupRecyclerView() {
        downloadAdapter = DownloadAdapter(
            onDownloadClick = { download -> onDownloadClicked(download) },
            onPauseClick = { download -> viewModel.pauseDownload(download.id) },
            onResumeClick = { download -> viewModel.resumeDownload(download.id) },
            onCancelClick = { download -> viewModel.cancelDownload(download.id) },
            onRetryClick = { download -> viewModel.retryDownload(download.id) },
            onDeleteClick = { download -> showDeleteConfirmation(download) }
        )

        recyclerView.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = downloadAdapter
        }
    }

    /**
     * إعداد زر الإضافة
     */
    private fun setupFab() {
        fabAddDownload.setOnClickListener {
            showAddDownloadDialog()
        }
    }

    /**
     * إعداد المراقبين للبيانات
     */
    private fun setupObservers() {
        // مراقبة قائمة التحميلات
        viewModel.downloads.observe(viewLifecycleOwner) { downloads ->
            downloadAdapter.updateDownloads(downloads)
            updateEmptyState(downloads.isEmpty())
        }

        // مراقبة حالة التحميل
        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            // عرض/إخفاء مؤشر التحميل
        }

        // مراقبة رسائل الخطأ
        viewModel.errorMessage.observe(viewLifecycleOwner) { errorMessage ->
            errorMessage?.let {
                showErrorMessage(it)
                viewModel.clearErrorMessage()
            }
        }
    }

    /**
     * تحميل التحميلات
     */
    private fun loadDownloads() {
        viewModel.loadDownloads()
    }

    /**
     * عرض dialog إضافة تحميل جديد
     */
    private fun showAddDownloadDialog() {
        val dialogView = LayoutInflater.from(requireContext())
            .inflate(R.layout.dialog_add_download, null)

        val urlInputLayout = dialogView.findViewById<TextInputLayout>(R.id.urlInputLayout)
        val urlEditText = dialogView.findViewById<TextInputEditText>(R.id.urlEditText)
        val qualitySpinner = dialogView.findViewById<Spinner>(R.id.qualitySpinner)
        val formatSpinner = dialogView.findViewById<Spinner>(R.id.formatSpinner)
        val downloadButton = dialogView.findViewById<Button>(R.id.downloadButton)
        val cancelButton = dialogView.findViewById<Button>(R.id.cancelButton)

        // إعداد قوائم الجودة والتنسيق
        setupQualitySpinner(qualitySpinner)
        setupFormatSpinner(formatSpinner)

        downloadDialog = AlertDialog.Builder(requireContext())
            .setView(dialogView)
            .setCancelable(true)
            .create()

        downloadDialog?.show()

        // زر التحميل
        downloadButton.setOnClickListener {
            val url = urlEditText.text?.toString()?.trim()

            if (url.isNullOrEmpty()) {
                urlInputLayout.error = getString(R.string.url_required)
                return@setOnClickListener
            }

            if (!isValidUrl(url)) {
                urlInputLayout.error = getString(R.string.invalid_url)
                return@setOnClickListener
            }

            val quality = qualitySpinner.selectedItem.toString()
            val format = formatSpinner.selectedItem.toString()

            // بدء التحميل
            viewModel.startDownload(url, quality, format)
            downloadDialog?.dismiss()

            // إظهار رسالة تأكيد
            Toast.makeText(requireContext(), "تم بدء التحميل", Toast.LENGTH_SHORT).show()

            // تحديث فوري للقائمة
            viewModel.loadDownloads()
        }

        // زر الإلغاء
        cancelButton.setOnClickListener {
            downloadDialog?.dismiss()
        }

        // مراقبة تغيير النص لإزالة الخطأ
        urlEditText.setOnTextChangedListener { text ->
            if (!text.isNullOrEmpty()) {
                urlInputLayout.error = null
            }
        }
    }

    /**
     * إعداد قائمة الجودة
     */
    private fun setupQualitySpinner(spinner: Spinner) {
        val qualities = arrayOf("Best", "720p", "480p", "360p", "Audio Only")

        val adapter = ArrayAdapter(requireContext(), android.R.layout.simple_spinner_item, qualities)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        spinner.adapter = adapter
    }

    /**
     * إعداد قائمة التنسيق
     */
    private fun setupFormatSpinner(spinner: Spinner) {
        val formats = arrayOf("MP3", "MP4", "M4A", "WEBM")

        val adapter = ArrayAdapter(requireContext(), android.R.layout.simple_spinner_item, formats)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        spinner.adapter = adapter
    }

    /**
     * فحص صحة الرابط
     */
    private fun isValidUrl(url: String): Boolean {
        return android.util.Patterns.WEB_URL.matcher(url).matches()
    }

    /**
     * عند النقر على تحميل
     */
    private fun onDownloadClicked(download: Download) {
        when (download.status) {
            com.musicplayer.pro.models.DownloadStatus.COMPLETED -> {
                // تشغيل الملف المحمل
                playDownloadedSong(download)
            }
            com.musicplayer.pro.models.DownloadStatus.FAILED -> {
                // عرض تفاصيل الخطأ
                showErrorDetails(download)
            }
            else -> {
                // عرض تفاصيل التحميل
                showDownloadDetails(download)
            }
        }
    }

    /**
     * عرض تأكيد الحذف
     */
    private fun showDeleteConfirmation(download: Download) {
        AlertDialog.Builder(requireContext())
            .setTitle(getString(R.string.delete))
            .setMessage("هل تريد حذف هذا التحميل؟")
            .setPositiveButton(getString(R.string.yes)) { _, _ ->
                viewModel.deleteDownload(download.id)
            }
            .setNegativeButton(getString(R.string.no), null)
            .show()
    }

    /**
     * عرض تفاصيل الخطأ
     */
    private fun showErrorDetails(download: Download) {
        AlertDialog.Builder(requireContext())
            .setTitle(getString(R.string.error))
            .setMessage(download.errorMessage ?: "خطأ غير معروف")
            .setPositiveButton(getString(R.string.ok), null)
            .setNeutralButton("إعادة المحاولة") { _, _ ->
                viewModel.retryDownload(download.id)
            }
            .show()
    }

    /**
     * عرض تفاصيل التحميل
     */
    private fun showDownloadDetails(download: Download) {
        val message = """
            العنوان: ${download.title}
            الفنان: ${download.artist}
            الحالة: ${getStatusText(download.status)}
            التقدم: ${download.getProgressPercentage()}%
            الحجم: ${download.getFormattedDownloadedSize()} / ${download.getFormattedTotalSize()}
            السرعة: ${download.speed}
            الوقت المتبقي: ${download.eta}
        """.trimIndent()

        AlertDialog.Builder(requireContext())
            .setTitle("تفاصيل التحميل")
            .setMessage(message)
            .setPositiveButton(getString(R.string.ok), null)
            .show()
    }

    /**
     * الحصول على نص الحالة
     */
    private fun getStatusText(status: com.musicplayer.pro.models.DownloadStatus): String {
        return when (status) {
            com.musicplayer.pro.models.DownloadStatus.PENDING -> "في الانتظار"
            com.musicplayer.pro.models.DownloadStatus.DOWNLOADING -> "جاري التحميل"
            com.musicplayer.pro.models.DownloadStatus.PAUSED -> "متوقف مؤقتاً"
            com.musicplayer.pro.models.DownloadStatus.COMPLETED -> "مكتمل"
            com.musicplayer.pro.models.DownloadStatus.FAILED -> "فاشل"
            com.musicplayer.pro.models.DownloadStatus.CANCELLED -> "ملغي"
        }
    }

    /**
     * تحديث حالة الفراغ
     */
    private fun updateEmptyState(isEmpty: Boolean) {
        if (isEmpty) {
            emptyStateLayout.visibility = View.VISIBLE
            recyclerView.visibility = View.GONE
        } else {
            emptyStateLayout.visibility = View.GONE
            recyclerView.visibility = View.VISIBLE
        }
    }

    /**
     * عرض رسالة خطأ
     */
    private fun showErrorMessage(message: String) {
        Toast.makeText(requireContext(), message, Toast.LENGTH_LONG).show()
    }

    /**
     * ربط MusicService مع ViewModel
     */
    private fun bindMusicServiceToViewModel() {
        val mainActivity = activity as? com.musicplayer.pro.MainActivity
        val musicService = mainActivity?.getMusicService()

        if (musicService != null) {
            // يمكن إضافة ربط ViewModel مع MusicService هنا إذا لزم الأمر
        } else {
            // إعادة المحاولة بعد تأخير قصير
            view?.postDelayed({
                bindMusicServiceToViewModel()
            }, 500)
        }
    }

    /**
     * تشغيل أغنية محملة
     */
    private fun playDownloadedSong(download: Download) {
        if (download.isCompleted() && download.filePath.isNotEmpty()) {
            // تحويل التحميل إلى أغنية
            val song = com.musicplayer.pro.models.Song(
                id = download.id.hashCode().toLong(),
                title = download.title,
                artist = download.artist,
                path = download.filePath,
                duration = download.duration.toLong() * 1000, // تحويل إلى milliseconds
                size = download.fileSize
            )

            // تشغيل الأغنية
            val mainActivity = activity as? com.musicplayer.pro.MainActivity
            val musicService = mainActivity?.getMusicService()
            musicService?.playSong(song, listOf(song))

            // إظهار الشريط السفلي
            mainActivity?.showBottomMusicBar(song)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        downloadDialog?.dismiss()
    }
}
