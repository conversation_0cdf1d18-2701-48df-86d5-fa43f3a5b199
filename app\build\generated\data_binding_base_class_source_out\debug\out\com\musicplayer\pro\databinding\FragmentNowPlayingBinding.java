// Generated by view binder compiler. Do not edit!
package com.musicplayer.pro.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.SeekBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.musicplayer.pro.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentNowPlayingBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ImageView albumCover;

  @NonNull
  public final CardView albumCoverCard;

  @NonNull
  public final TextView albumName;

  @NonNull
  public final TextView artistName;

  @NonNull
  public final ImageView backgroundImage;

  @NonNull
  public final ImageButton btnFavorite;

  @NonNull
  public final ImageButton btnNext;

  @NonNull
  public final FloatingActionButton btnPlayPause;

  @NonNull
  public final ImageButton btnPlaylist;

  @NonNull
  public final ImageButton btnPrevious;

  @NonNull
  public final ImageButton btnRepeat;

  @NonNull
  public final ImageButton btnShare;

  @NonNull
  public final ImageButton btnShuffle;

  @NonNull
  public final LinearLayout controlsLayout;

  @NonNull
  public final TextView currentTime;

  @NonNull
  public final LinearLayout progressLayout;

  @NonNull
  public final LinearLayout secondaryControlsLayout;

  @NonNull
  public final SeekBar seekBar;

  @NonNull
  public final LinearLayout songInfoLayout;

  @NonNull
  public final TextView songTitle;

  @NonNull
  public final TextView totalTime;

  private FragmentNowPlayingBinding(@NonNull ConstraintLayout rootView,
      @NonNull ImageView albumCover, @NonNull CardView albumCoverCard, @NonNull TextView albumName,
      @NonNull TextView artistName, @NonNull ImageView backgroundImage,
      @NonNull ImageButton btnFavorite, @NonNull ImageButton btnNext,
      @NonNull FloatingActionButton btnPlayPause, @NonNull ImageButton btnPlaylist,
      @NonNull ImageButton btnPrevious, @NonNull ImageButton btnRepeat,
      @NonNull ImageButton btnShare, @NonNull ImageButton btnShuffle,
      @NonNull LinearLayout controlsLayout, @NonNull TextView currentTime,
      @NonNull LinearLayout progressLayout, @NonNull LinearLayout secondaryControlsLayout,
      @NonNull SeekBar seekBar, @NonNull LinearLayout songInfoLayout, @NonNull TextView songTitle,
      @NonNull TextView totalTime) {
    this.rootView = rootView;
    this.albumCover = albumCover;
    this.albumCoverCard = albumCoverCard;
    this.albumName = albumName;
    this.artistName = artistName;
    this.backgroundImage = backgroundImage;
    this.btnFavorite = btnFavorite;
    this.btnNext = btnNext;
    this.btnPlayPause = btnPlayPause;
    this.btnPlaylist = btnPlaylist;
    this.btnPrevious = btnPrevious;
    this.btnRepeat = btnRepeat;
    this.btnShare = btnShare;
    this.btnShuffle = btnShuffle;
    this.controlsLayout = controlsLayout;
    this.currentTime = currentTime;
    this.progressLayout = progressLayout;
    this.secondaryControlsLayout = secondaryControlsLayout;
    this.seekBar = seekBar;
    this.songInfoLayout = songInfoLayout;
    this.songTitle = songTitle;
    this.totalTime = totalTime;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentNowPlayingBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentNowPlayingBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_now_playing, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentNowPlayingBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.albumCover;
      ImageView albumCover = ViewBindings.findChildViewById(rootView, id);
      if (albumCover == null) {
        break missingId;
      }

      id = R.id.albumCoverCard;
      CardView albumCoverCard = ViewBindings.findChildViewById(rootView, id);
      if (albumCoverCard == null) {
        break missingId;
      }

      id = R.id.albumName;
      TextView albumName = ViewBindings.findChildViewById(rootView, id);
      if (albumName == null) {
        break missingId;
      }

      id = R.id.artistName;
      TextView artistName = ViewBindings.findChildViewById(rootView, id);
      if (artistName == null) {
        break missingId;
      }

      id = R.id.backgroundImage;
      ImageView backgroundImage = ViewBindings.findChildViewById(rootView, id);
      if (backgroundImage == null) {
        break missingId;
      }

      id = R.id.btnFavorite;
      ImageButton btnFavorite = ViewBindings.findChildViewById(rootView, id);
      if (btnFavorite == null) {
        break missingId;
      }

      id = R.id.btnNext;
      ImageButton btnNext = ViewBindings.findChildViewById(rootView, id);
      if (btnNext == null) {
        break missingId;
      }

      id = R.id.btnPlayPause;
      FloatingActionButton btnPlayPause = ViewBindings.findChildViewById(rootView, id);
      if (btnPlayPause == null) {
        break missingId;
      }

      id = R.id.btnPlaylist;
      ImageButton btnPlaylist = ViewBindings.findChildViewById(rootView, id);
      if (btnPlaylist == null) {
        break missingId;
      }

      id = R.id.btnPrevious;
      ImageButton btnPrevious = ViewBindings.findChildViewById(rootView, id);
      if (btnPrevious == null) {
        break missingId;
      }

      id = R.id.btnRepeat;
      ImageButton btnRepeat = ViewBindings.findChildViewById(rootView, id);
      if (btnRepeat == null) {
        break missingId;
      }

      id = R.id.btnShare;
      ImageButton btnShare = ViewBindings.findChildViewById(rootView, id);
      if (btnShare == null) {
        break missingId;
      }

      id = R.id.btnShuffle;
      ImageButton btnShuffle = ViewBindings.findChildViewById(rootView, id);
      if (btnShuffle == null) {
        break missingId;
      }

      id = R.id.controlsLayout;
      LinearLayout controlsLayout = ViewBindings.findChildViewById(rootView, id);
      if (controlsLayout == null) {
        break missingId;
      }

      id = R.id.currentTime;
      TextView currentTime = ViewBindings.findChildViewById(rootView, id);
      if (currentTime == null) {
        break missingId;
      }

      id = R.id.progressLayout;
      LinearLayout progressLayout = ViewBindings.findChildViewById(rootView, id);
      if (progressLayout == null) {
        break missingId;
      }

      id = R.id.secondaryControlsLayout;
      LinearLayout secondaryControlsLayout = ViewBindings.findChildViewById(rootView, id);
      if (secondaryControlsLayout == null) {
        break missingId;
      }

      id = R.id.seekBar;
      SeekBar seekBar = ViewBindings.findChildViewById(rootView, id);
      if (seekBar == null) {
        break missingId;
      }

      id = R.id.songInfoLayout;
      LinearLayout songInfoLayout = ViewBindings.findChildViewById(rootView, id);
      if (songInfoLayout == null) {
        break missingId;
      }

      id = R.id.songTitle;
      TextView songTitle = ViewBindings.findChildViewById(rootView, id);
      if (songTitle == null) {
        break missingId;
      }

      id = R.id.totalTime;
      TextView totalTime = ViewBindings.findChildViewById(rootView, id);
      if (totalTime == null) {
        break missingId;
      }

      return new FragmentNowPlayingBinding((ConstraintLayout) rootView, albumCover, albumCoverCard,
          albumName, artistName, backgroundImage, btnFavorite, btnNext, btnPlayPause, btnPlaylist,
          btnPrevious, btnRepeat, btnShare, btnShuffle, controlsLayout, currentTime, progressLayout,
          secondaryControlsLayout, seekBar, songInfoLayout, songTitle, totalTime);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
