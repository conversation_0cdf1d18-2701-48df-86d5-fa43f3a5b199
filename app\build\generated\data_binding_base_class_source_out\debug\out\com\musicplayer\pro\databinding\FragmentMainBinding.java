// Generated by view binder compiler. Do not edit!
package com.musicplayer.pro.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.android.material.progressindicator.LinearProgressIndicator;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import com.musicplayer.pro.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentMainBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final LinearLayout emptyStateLayout;

  @NonNull
  public final FloatingActionButton fabShuffle;

  @NonNull
  public final LinearProgressIndicator loadingProgressBar;

  @NonNull
  public final RecyclerView recyclerViewSongs;

  @NonNull
  public final TextInputEditText searchEditText;

  @NonNull
  public final TextInputLayout searchInputLayout;

  @NonNull
  public final TextView statusText;

  private FragmentMainBinding(@NonNull CoordinatorLayout rootView,
      @NonNull LinearLayout emptyStateLayout, @NonNull FloatingActionButton fabShuffle,
      @NonNull LinearProgressIndicator loadingProgressBar, @NonNull RecyclerView recyclerViewSongs,
      @NonNull TextInputEditText searchEditText, @NonNull TextInputLayout searchInputLayout,
      @NonNull TextView statusText) {
    this.rootView = rootView;
    this.emptyStateLayout = emptyStateLayout;
    this.fabShuffle = fabShuffle;
    this.loadingProgressBar = loadingProgressBar;
    this.recyclerViewSongs = recyclerViewSongs;
    this.searchEditText = searchEditText;
    this.searchInputLayout = searchInputLayout;
    this.statusText = statusText;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.emptyStateLayout;
      LinearLayout emptyStateLayout = ViewBindings.findChildViewById(rootView, id);
      if (emptyStateLayout == null) {
        break missingId;
      }

      id = R.id.fabShuffle;
      FloatingActionButton fabShuffle = ViewBindings.findChildViewById(rootView, id);
      if (fabShuffle == null) {
        break missingId;
      }

      id = R.id.loadingProgressBar;
      LinearProgressIndicator loadingProgressBar = ViewBindings.findChildViewById(rootView, id);
      if (loadingProgressBar == null) {
        break missingId;
      }

      id = R.id.recyclerViewSongs;
      RecyclerView recyclerViewSongs = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewSongs == null) {
        break missingId;
      }

      id = R.id.searchEditText;
      TextInputEditText searchEditText = ViewBindings.findChildViewById(rootView, id);
      if (searchEditText == null) {
        break missingId;
      }

      id = R.id.searchInputLayout;
      TextInputLayout searchInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (searchInputLayout == null) {
        break missingId;
      }

      id = R.id.statusText;
      TextView statusText = ViewBindings.findChildViewById(rootView, id);
      if (statusText == null) {
        break missingId;
      }

      return new FragmentMainBinding((CoordinatorLayout) rootView, emptyStateLayout, fabShuffle,
          loadingProgressBar, recyclerViewSongs, searchEditText, searchInputLayout, statusText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
