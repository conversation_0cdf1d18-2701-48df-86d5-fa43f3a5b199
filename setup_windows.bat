@echo off
chcp 65001 >nul
title Music Player Pro - Kotlin Setup

echo.
echo ========================================
echo 🎵 Music Player Pro - Kotlin Setup
echo ========================================
echo.

:: التحقق من Python
echo 📝 فحص Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير موجود. يرجى تثبيت Python 3.8+ أولاً
    echo 📥 تحميل من: https://www.python.org/downloads/
    pause
    exit /b 1
)
echo ✅ Python موجود

:: التحقق من Git
echo 📝 فحص Git...
git --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Git غير موجود. يرجى تثبيت Git أولاً
    echo 📥 تحميل من: https://git-scm.com/download/win
    pause
    exit /b 1
)
echo ✅ Git موجود

:: إنشاء مجلد الأدوات
echo 📁 إنشاء مجلدات المشروع...
if not exist "tools" mkdir tools
if not exist "android-sdk" mkdir android-sdk
if not exist "app\src\main\res" mkdir app\src\main\res
if not exist "app\src\main\java\com\musicplayer\pro" mkdir app\src\main\java\com\musicplayer\pro

:: تحميل Java إذا لم يكن موجوداً
echo 📝 فحص Java...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 📥 تحميل وتثبيت OpenJDK...
    
    :: تحميل OpenJDK
    if not exist "tools\openjdk.zip" (
        echo 📥 تحميل OpenJDK 11...
        powershell -Command "Invoke-WebRequest -Uri 'https://download.java.net/java/GA/jdk11/9/GPL/openjdk-11.0.2_windows-x64_bin.zip' -OutFile 'tools\openjdk.zip'"
    )
    
    :: استخراج Java
    if not exist "tools\java" (
        echo 📦 استخراج Java...
        powershell -Command "Expand-Archive -Path 'tools\openjdk.zip' -DestinationPath 'tools\java' -Force"
    )
    
    :: إعداد متغيرات البيئة
    set "JAVA_HOME=%cd%\tools\java\jdk-11.0.2"
    set "PATH=%JAVA_HOME%\bin;%PATH%"
    
    echo ✅ تم تثبيت Java
) else (
    echo ✅ Java موجود
)

:: تحميل Android SDK
echo 📥 تحميل Android SDK...
if not exist "tools\android-sdk-tools.zip" (
    echo 📥 تحميل Android Command Line Tools...
    powershell -Command "Invoke-WebRequest -Uri 'https://dl.google.com/android/repository/commandlinetools-win-9477386_latest.zip' -OutFile 'tools\android-sdk-tools.zip'"
)

:: استخراج SDK
if not exist "android-sdk\cmdline-tools\latest" (
    echo 📦 استخراج Android SDK...
    powershell -Command "Expand-Archive -Path 'tools\android-sdk-tools.zip' -DestinationPath 'android-sdk\cmdline-tools' -Force"
    
    :: إعادة تنظيم المجلدات
    if exist "android-sdk\cmdline-tools\cmdline-tools" (
        move "android-sdk\cmdline-tools\cmdline-tools" "android-sdk\cmdline-tools\latest"
    )
)

:: إعداد متغيرات البيئة لـ Android
set "ANDROID_HOME=%cd%\android-sdk"
set "ANDROID_SDK_ROOT=%cd%\android-sdk"
set "PATH=%ANDROID_HOME%\cmdline-tools\latest\bin;%ANDROID_HOME%\platform-tools;%PATH%"

:: تثبيت مكونات Android SDK
echo 📦 تثبيت مكونات Android SDK...
if exist "android-sdk\cmdline-tools\latest\bin\sdkmanager.bat" (
    echo y | android-sdk\cmdline-tools\latest\bin\sdkmanager.bat --licenses >nul 2>&1
    
    echo 📦 تثبيت Platform Tools...
    android-sdk\cmdline-tools\latest\bin\sdkmanager.bat "platform-tools" >nul
    
    echo 📦 تثبيت Android 34...
    android-sdk\cmdline-tools\latest\bin\sdkmanager.bat "platforms;android-34" >nul
    
    echo 📦 تثبيت Build Tools...
    android-sdk\cmdline-tools\latest\bin\sdkmanager.bat "build-tools;34.0.0" >nul
    
    echo 📦 تثبيت NDK...
    android-sdk\cmdline-tools\latest\bin\sdkmanager.bat "ndk;25.2.9519653" >nul
    
    echo ✅ تم تثبيت مكونات Android SDK
)

:: تحميل Gradle
echo 📥 تحميل Gradle...
if not exist "tools\gradle.zip" (
    echo 📥 تحميل Gradle 8.4...
    powershell -Command "Invoke-WebRequest -Uri 'https://services.gradle.org/distributions/gradle-8.4-bin.zip' -OutFile 'tools\gradle.zip'"
)

:: استخراج Gradle
if not exist "tools\gradle\gradle-8.4" (
    echo 📦 استخراج Gradle...
    powershell -Command "Expand-Archive -Path 'tools\gradle.zip' -DestinationPath 'tools\gradle' -Force"
)

:: إضافة Gradle إلى PATH
set "PATH=%cd%\tools\gradle\gradle-8.4\bin;%PATH%"

:: إنشاء ملفات Gradle المطلوبة
echo 📝 إنشاء ملفات المشروع...

:: إنشاء gradle.properties
if not exist "gradle.properties" (
    echo 📝 إنشاء gradle.properties...
    (
        echo org.gradle.jvmargs=-Xmx4g -Dfile.encoding=UTF-8
        echo org.gradle.parallel=true
        echo org.gradle.caching=true
        echo android.useAndroidX=true
        echo android.enableJetifier=true
        echo kotlin.code.style=official
    ) > gradle.properties
)

:: إنشاء settings.gradle
if not exist "settings.gradle" (
    echo 📝 إنشاء settings.gradle...
    (
        echo pluginManagement {
        echo     repositories {
        echo         google^(^)
        echo         mavenCentral^(^)
        echo         gradlePluginPortal^(^)
        echo     }
        echo }
        echo.
        echo dependencyResolutionManagement {
        echo     repositoriesMode.set^(RepositoriesMode.FAIL_ON_PROJECT_REPOS^)
        echo     repositories {
        echo         google^(^)
        echo         mavenCentral^(^)
        echo         maven { url 'https://jitpack.io' }
        echo     }
        echo }
        echo.
        echo rootProject.name = "MusicPlayerPro"
        echo include ':app'
    ) > settings.gradle
)

:: إنشاء build.gradle الجذر
if not exist "build.gradle" (
    echo 📝 إنشاء build.gradle الجذر...
    (
        echo buildscript {
        echo     ext.kotlin_version = "1.9.21"
        echo     dependencies {
        echo         classpath 'com.android.tools.build:gradle:8.2.0'
        echo         classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        echo     }
        echo }
        echo.
        echo plugins {
        echo     id 'com.android.application' version '8.2.0' apply false
        echo     id 'org.jetbrains.kotlin.android' version '1.9.21' apply false
        echo }
        echo.
        echo task clean^(type: Delete^) {
        echo     delete rootProject.buildDir
        echo }
    ) > build.gradle
)

:: إنشاء local.properties
echo 📝 إنشاء local.properties...
(
    echo sdk.dir=%ANDROID_HOME:\=\\%
    echo ndk.dir=%ANDROID_HOME:\=\\%\\ndk\\25.2.9519653
) > local.properties

:: إنشاء Gradle Wrapper
echo 📝 إنشاء Gradle Wrapper...
if not exist "gradlew.bat" (
    gradle wrapper --gradle-version 8.4 >nul 2>&1
)

:: تشغيل سكريبت Python للإعداد المتقدم
echo 🐍 تشغيل سكريبت الإعداد المتقدم...
python setup_and_run.py

:: فحص نتيجة البناء
if exist "app\build\outputs\apk\debug\*.apk" (
    echo.
    echo ========================================
    echo ✅ تم بناء المشروع بنجاح!
    echo ========================================
    echo.
    echo 📱 ملف APK موجود في: app\build\outputs\apk\debug\
    echo 🚀 يمكنك الآن فتح المشروع في Android Studio
    echo 📁 مجلد المشروع: %cd%
    echo.
    
    :: عرض معلومات APK
    for %%f in (app\build\outputs\apk\debug\*.apk) do (
        echo 📱 APK: %%f
        echo 📊 الحجم: 
        powershell -Command "'{0:N2} MB' -f ((Get-Item '%%f').Length / 1MB)"
    )
    
) else (
    echo.
    echo ========================================
    echo ❌ فشل في بناء المشروع
    echo ========================================
    echo.
    echo 🔍 تحقق من الأخطاء أعلاه
    echo 📝 يمكنك المحاولة يدوياً بـ: gradlew assembleDebug
)

echo.
echo 📋 الأوامر المفيدة:
echo   gradlew assembleDebug    - بناء APK للتطوير
echo   gradlew assembleRelease  - بناء APK للإنتاج
echo   gradlew clean           - تنظيف المشروع
echo   gradlew build           - بناء كامل
echo.

pause
