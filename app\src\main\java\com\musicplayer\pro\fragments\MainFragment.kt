package com.musicplayer.pro.fragments

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.floatingactionbutton.FloatingActionButton
import com.musicplayer.pro.R
import com.musicplayer.pro.adapters.SongAdapter
import com.musicplayer.pro.models.Song
import com.musicplayer.pro.viewmodels.MusicViewModel

/**
 * الشاشة الرئيسية - تعرض قائمة الأغاني
 * مطابقة لـ MainScreen في Python
 */
class MainFragment : Fragment() {
    
    private lateinit var recyclerView: RecyclerView
    private lateinit var songAdapter: SongAdapter
    private lateinit var fabShuffle: FloatingActionButton
    private lateinit var viewModel: MusicViewModel
    private lateinit var statusText: TextView

    // BroadcastReceiver لاستقبال نتائج الفحص
    private val scanReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent?.action == "com.musicplayer.pro.SONGS_SCANNED") {
                val songCount = intent.getIntExtra("song_count", 0)
                val songs = intent.getParcelableArrayListExtra<Song>("songs_list") ?: arrayListOf()

                updateScanStatus(songCount)
                updateSongsList(songs)
            }
        }
    }
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_main, container, false)
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        // تهيئة ViewModel
        viewModel = ViewModelProvider(requireActivity())[MusicViewModel::class.java]
        
        // تهيئة UI
        initializeUI(view)
        
        // إعداد المراقبين
        setupObservers()
        
        // تحميل الأغاني
        loadSongs()

        // ربط MusicService مع ViewModel
        bindMusicServiceToViewModel()

        // تسجيل BroadcastReceiver
        try {
            val filter = IntentFilter("com.musicplayer.pro.SONGS_SCANNED")
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
                requireContext().registerReceiver(scanReceiver, filter, Context.RECEIVER_NOT_EXPORTED)
            } else {
                requireContext().registerReceiver(scanReceiver, filter)
            }
        } catch (e: Exception) {
            // تجاهل الخطأ إذا فشل التسجيل
        }
    }
    
    /**
     * تهيئة واجهة المستخدم
     */
    private fun initializeUI(view: View) {
        recyclerView = view.findViewById(R.id.recyclerViewSongs)
        fabShuffle = view.findViewById(R.id.fabShuffle)
        statusText = view.findViewById(R.id.statusText)

        setupRecyclerView()
        setupFab()
    }
    
    /**
     * إعداد RecyclerView
     */
    private fun setupRecyclerView() {
        songAdapter = SongAdapter(
            onSongClick = { song -> onSongClicked(song) },
            onSongLongClick = { song -> onSongLongClicked(song) },
            onFavoriteClick = { song -> toggleFavorite(song) }
        )
        
        recyclerView.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = songAdapter
        }
    }
    
    /**
     * إعداد زر التشغيل العشوائي
     */
    private fun setupFab() {
        fabShuffle.setOnClickListener {
            shuffleAndPlay()
        }
    }
    
    /**
     * إعداد المراقبين للبيانات
     */
    private fun setupObservers() {
        // مراقبة قائمة الأغاني
        viewModel.songs.observe(viewLifecycleOwner) { songs ->
            songAdapter.updateSongs(songs)
        }
        
        // مراقبة الأغنية الحالية
        viewModel.currentSong.observe(viewLifecycleOwner) { song ->
            songAdapter.setCurrentSong(song)
        }
        
        // مراقبة حالة التشغيل
        viewModel.isPlaying.observe(viewLifecycleOwner) { isPlaying ->
            songAdapter.setPlayingState(isPlaying)
        }
        
        // مراقبة حالة التحميل
        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            showLoadingState(isLoading)
        }
    }
    
    /**
     * تحميل الأغاني
     */
    private fun loadSongs() {
        viewModel.loadSongs()
    }
    
    /**
     * عند النقر على أغنية
     */
    private fun onSongClicked(song: Song) {
        val allSongs = songAdapter.getSongs()
        viewModel.playSong(song, allSongs)

        // إظهار الشريط السفلي
        (activity as? com.musicplayer.pro.MainActivity)?.showBottomMusicBar(song)

        // الانتقال لشاشة التشغيل
        (activity as? com.musicplayer.pro.MainActivity)?.let { mainActivity ->
            val viewPager = mainActivity.findViewById<androidx.viewpager2.widget.ViewPager2>(R.id.viewPager)
            viewPager?.currentItem = 1
        }
    }
    
    /**
     * عند الضغط المطول على أغنية
     */
    private fun onSongLongClicked(song: Song) {
        showSongOptionsDialog(song)
    }
    
    /**
     * تبديل حالة المفضلة
     */
    private fun toggleFavorite(song: Song) {
        viewModel.toggleFavorite(song)
    }
    
    /**
     * تشغيل عشوائي
     */
    private fun shuffleAndPlay() {
        val songs = songAdapter.getSongs()
        if (songs.isNotEmpty()) {
            viewModel.shuffleAndPlay(songs)
        }
    }
    
    /**
     * عرض حالة التحميل
     */
    private fun showLoadingState(isLoading: Boolean) {
        // يمكن إضافة ProgressBar هنا
    }
    
    /**
     * عرض خيارات الأغنية
     */
    private fun showSongOptionsDialog(song: Song) {
        // إنشاء dialog مع خيارات مثل:
        // - إضافة إلى قائمة تشغيل
        // - حذف
        // - تفاصيل الأغنية
        // - مشاركة
    }

    /**
     * تحديث حالة الفحص
     */
    private fun updateScanStatus(songCount: Int) {
        val message = when {
            songCount == 0 -> "لم يتم العثور على أغاني"
            songCount == 1 -> "تم العثور على أغنية واحدة"
            songCount <= 10 -> "تم العثور على $songCount أغاني"
            else -> "تم العثور على $songCount أغنية 🎵"
        }

        statusText.text = message
        statusText.visibility = View.VISIBLE

        // إخفاء الرسالة بعد 3 ثوان
        statusText.postDelayed({
            statusText.visibility = View.GONE
        }, 3000)
    }

    /**
     * تحديث قائمة الأغاني
     */
    private fun updateSongsList(songs: List<Song>) {
        try {
            // تحديث الـ adapter
            songAdapter.updateSongs(songs)

            // إخفاء/إظهار empty state
            if (songs.isEmpty()) {
                recyclerView.visibility = View.GONE
                view?.findViewById<View>(R.id.emptyStateLayout)?.visibility = View.VISIBLE
            } else {
                recyclerView.visibility = View.VISIBLE
                view?.findViewById<View>(R.id.emptyStateLayout)?.visibility = View.GONE
            }

            // تحديث ViewModel
            viewModel.updateSongs(songs)

        } catch (e: Exception) {
            android.util.Log.e("MainFragment", "خطأ في تحديث قائمة الأغاني: ${e.message}")
        }
    }

    /**
     * ربط MusicService مع ViewModel
     */
    private fun bindMusicServiceToViewModel() {
        val mainActivity = activity as? com.musicplayer.pro.MainActivity
        val musicService = mainActivity?.getMusicService()

        if (musicService != null) {
            viewModel.bindMusicService(musicService)
        } else {
            // إعادة المحاولة بعد تأخير قصير
            view?.postDelayed({
                bindMusicServiceToViewModel()
            }, 500)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        // إلغاء تسجيل BroadcastReceiver
        try {
            requireContext().unregisterReceiver(scanReceiver)
        } catch (e: Exception) {
            // تجاهل الخطأ إذا لم يكن مسجلاً
        }
    }
}
