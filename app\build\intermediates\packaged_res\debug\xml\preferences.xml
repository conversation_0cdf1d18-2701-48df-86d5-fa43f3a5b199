<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <!-- إعدادات المظهر -->
    <PreferenceCategory
        android:title="@string/appearance_settings"
        android:icon="@drawable/ic_palette">

        <ListPreference
            android:key="theme_preference"
            android:title="@string/theme"
            android:summary="@string/theme_summary"
            android:entries="@array/theme_entries"
            android:entryValues="@array/theme_values"
            android:defaultValue="auto"
            android:icon="@drawable/ic_theme" />

        <ListPreference
            android:key="language_preference"
            android:title="@string/language"
            android:summary="@string/language_summary"
            android:entries="@array/language_entries"
            android:entryValues="@array/language_values"
            android:defaultValue="auto"
            android:icon="@drawable/ic_language" />

    </PreferenceCategory>

    <!-- إعدادات الصوت -->
    <PreferenceCategory
        android:title="@string/audio_settings"
        android:icon="@drawable/ic_audio">

        <ListPreference
            android:key="audio_quality_preference"
            android:title="@string/audio_quality"
            android:summary="@string/audio_quality_summary"
            android:entries="@array/audio_quality_entries"
            android:entryValues="@array/audio_quality_values"
            android:defaultValue="high"
            android:icon="@drawable/ic_high_quality" />

        <Preference
            android:key="equalizer_preference"
            android:title="@string/equalizer"
            android:summary="@string/equalizer_summary"
            android:icon="@drawable/ic_equalizer" />

        <SwitchPreferenceCompat
            android:key="audio_effects_preference"
            android:title="@string/audio_effects"
            android:summary="@string/audio_effects_summary"
            android:defaultValue="true"
            android:icon="@drawable/ic_audio_effects" />

        <SwitchPreferenceCompat
            android:key="bass_boost_preference"
            android:title="@string/bass_boost"
            android:summary="@string/bass_boost_summary"
            android:defaultValue="false"
            android:dependency="audio_effects_preference"
            android:icon="@drawable/ic_bass" />

        <SwitchPreferenceCompat
            android:key="virtualizer_preference"
            android:title="@string/virtualizer"
            android:summary="@string/virtualizer_summary"
            android:defaultValue="false"
            android:dependency="audio_effects_preference"
            android:icon="@drawable/ic_surround_sound" />

    </PreferenceCategory>

    <!-- إعدادات التحميل -->
    <PreferenceCategory
        android:title="@string/download_settings"
        android:icon="@drawable/ic_download">

        <Preference
            android:key="download_folder_preference"
            android:title="@string/download_folder"
            android:summary="@string/download_folder_summary"
            android:icon="@drawable/ic_folder" />

        <ListPreference
            android:key="default_download_quality"
            android:title="@string/default_download_quality"
            android:summary="@string/default_download_quality_summary"
            android:entries="@array/download_quality_entries"
            android:entryValues="@array/download_quality_values"
            android:defaultValue="best"
            android:icon="@drawable/ic_download_quality" />

        <ListPreference
            android:key="default_download_format"
            android:title="@string/default_download_format"
            android:summary="@string/default_download_format_summary"
            android:entries="@array/download_format_entries"
            android:entryValues="@array/download_format_values"
            android:defaultValue="mp3"
            android:icon="@drawable/ic_file_format" />

        <SwitchPreferenceCompat
            android:key="auto_download_thumbnails"
            android:title="@string/auto_download_thumbnails"
            android:summary="@string/auto_download_thumbnails_summary"
            android:defaultValue="true"
            android:icon="@drawable/ic_image" />

        <SwitchPreferenceCompat
            android:key="wifi_only_downloads"
            android:title="@string/wifi_only_downloads"
            android:summary="@string/wifi_only_downloads_summary"
            android:defaultValue="false"
            android:icon="@drawable/ic_wifi" />

    </PreferenceCategory>

    <!-- إعدادات التخزين -->
    <PreferenceCategory
        android:title="@string/storage_settings"
        android:icon="@drawable/ic_storage">

        <Preference
            android:key="clear_cache_preference"
            android:title="@string/clear_cache"
            android:summary="@string/clear_cache_summary"
            android:icon="@drawable/ic_clear" />

        <Preference
            android:key="backup_data_preference"
            android:title="@string/backup_data"
            android:summary="@string/backup_data_summary"
            android:icon="@drawable/ic_backup" />

        <Preference
            android:key="restore_data_preference"
            android:title="@string/restore_data"
            android:summary="@string/restore_data_summary"
            android:icon="@drawable/ic_restore" />

        <Preference
            android:key="reset_settings_preference"
            android:title="@string/reset_settings"
            android:summary="@string/reset_settings_summary"
            android:icon="@drawable/ic_reset" />

    </PreferenceCategory>

    <!-- حول التطبيق -->
    <PreferenceCategory
        android:title="@string/about_app"
        android:icon="@drawable/ic_info">

        <Preference
            android:key="app_info_preference"
            android:title="@string/app_info"
            android:icon="@drawable/ic_info" />

        <Preference
            android:key="check_updates_preference"
            android:title="@string/check_updates"
            android:summary="@string/check_updates_summary"
            android:icon="@drawable/ic_update" />

        <Preference
            android:key="rate_app_preference"
            android:title="@string/rate_app"
            android:summary="@string/rate_app_summary"
            android:icon="@drawable/ic_star" />

        <Preference
            android:key="share_app_preference"
            android:title="@string/share_app"
            android:summary="@string/share_app_summary"
            android:icon="@drawable/ic_share" />

        <Preference
            android:key="license_preference"
            android:title="@string/license"
            android:summary="@string/license_summary"
            android:icon="@drawable/ic_license" />

    </PreferenceCategory>

</PreferenceScreen>
