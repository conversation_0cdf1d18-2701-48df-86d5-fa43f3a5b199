#Sun Jul 13 14:58:23 AST 2025
com.musicplayer.pro.app-main-59\:/drawable/ic_folder.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_folder.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_pause.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_pause.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/bottom_bar_background.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_bottom_bar_background.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_clear.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_clear.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/quality_badge.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_quality_badge.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_download_quality.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_download_quality.xml.flat
com.musicplayer.pro.app-main-59\:/xml/file_paths.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\xml_file_paths.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_music_note.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_music_note.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_repeat_off.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_repeat_off.xml.flat
com.musicplayer.pro.app-main-59\:/xml/automotive_app_desc.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\xml_automotive_app_desc.xml.flat
com.musicplayer.pro.app-mergeDebugResources-56\:/layout/item_song.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\layout_item_song.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/playing_overlay.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_playing_overlay.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_language.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_language.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_audio_effects.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_audio_effects.xml.flat
com.musicplayer.pro.app-main-59\:/menu/main_menu.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\menu_main_menu.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_equalizer.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_equalizer.xml.flat
com.musicplayer.pro.app-mergeDebugResources-56\:/layout/dialog_add_download.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\layout_dialog_add_download.xml.flat
com.musicplayer.pro.app-mergeDebugResources-56\:/layout/activity_main.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_main.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_info.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_info.xml.flat
com.musicplayer.pro.app-mergeDebugResources-56\:/layout/fragment_main.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\layout_fragment_main.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_music_library.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_music_library.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_audio.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_audio.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_shuffle_on.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_shuffle_on.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/control_button_background.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_control_button_background.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_loading.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_loading.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_backup.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_backup.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_share.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_share.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_warning.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_warning.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_playlist_add.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_playlist_add.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_now_playing.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_now_playing.xml.flat
com.musicplayer.pro.app-mergeDebugResources-56\:/layout/item_equalizer_band.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\layout_item_equalizer_band.xml.flat
com.musicplayer.pro.app-mergeDebugResources-56\:/layout/item_download.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\layout_item_download.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_update.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_update.xml.flat
com.musicplayer.pro.app-main-59\:/mipmap-hdpi/ic_launcher.png=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\mipmap-hdpi_ic_launcher.png.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_license.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_license.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/play_button_background.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_play_button_background.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_repeat_all.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_repeat_all.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_favorite_filled.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_favorite_filled.xml.flat
com.musicplayer.pro.app-mergeDebugResources-56\:/layout/activity_equalizer.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_equalizer.xml.flat
com.musicplayer.pro.app-mergeDebugResources-56\:/layout/activity_settings.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_settings.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_theme.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_theme.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_search.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_search.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_add.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_add.xml.flat
com.musicplayer.pro.app-main-59\:/xml/preferences.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\xml_preferences.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_delete.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_delete.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_skip_previous.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_skip_previous.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_shuffle_off.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_shuffle_off.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_bass.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_bass.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_download.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_download.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_skip_next.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_skip_next.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_palette.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_palette.xml.flat
com.musicplayer.pro.app-mergeDebugResources-56\:/layout/fragment_download.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\layout_fragment_download.xml.flat
com.musicplayer.pro.app-main-59\:/xml/backup_rules.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\xml_backup_rules.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_image.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_image.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_exit.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_exit.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_file_format.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_file_format.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_star.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_star.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_refresh.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_refresh.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_surround_sound.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_surround_sound.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_repeat_one.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_repeat_one.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/default_album_cover.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_default_album_cover.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_settings.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_settings.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_reset.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_reset.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/spinner_background.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_spinner_background.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_storage.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_storage.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_restore.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_restore.xml.flat
com.musicplayer.pro.app-mergeDebugResources-56\:/layout/bottom_music_bar.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\layout_bottom_music_bar.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_favorite_outline.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_favorite_outline.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_play.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_play.xml.flat
com.musicplayer.pro.app-main-59\:/mipmap-hdpi/ic_launcher_round.png=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\mipmap-hdpi_ic_launcher_round.png.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_high_quality.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_high_quality.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_wifi.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_wifi.xml.flat
com.musicplayer.pro.app-main-59\:/drawable/ic_link.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_link.xml.flat
com.musicplayer.pro.app-mergeDebugResources-56\:/layout/fragment_now_playing.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\layout_fragment_now_playing.xml.flat
com.musicplayer.pro.app-main-59\:/xml/data_extraction_rules.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\merged_res\\debug\\xml_data_extraction_rules.xml.flat
