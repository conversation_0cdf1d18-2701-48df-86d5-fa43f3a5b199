// Generated by view binder compiler. Do not edit!
package com.musicplayer.pro.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.musicplayer.pro.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemSongBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ImageView albumCover;

  @NonNull
  public final CardView albumCoverCard;

  @NonNull
  public final TextView artistName;

  @NonNull
  public final TextView duration;

  @NonNull
  public final ImageView favoriteIcon;

  @NonNull
  public final LinearLayout songInfoLayout;

  @NonNull
  public final TextView songTitle;

  private ItemSongBinding(@NonNull ConstraintLayout rootView, @NonNull ImageView albumCover,
      @NonNull CardView albumCoverCard, @NonNull TextView artistName, @NonNull TextView duration,
      @NonNull ImageView favoriteIcon, @NonNull LinearLayout songInfoLayout,
      @NonNull TextView songTitle) {
    this.rootView = rootView;
    this.albumCover = albumCover;
    this.albumCoverCard = albumCoverCard;
    this.artistName = artistName;
    this.duration = duration;
    this.favoriteIcon = favoriteIcon;
    this.songInfoLayout = songInfoLayout;
    this.songTitle = songTitle;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemSongBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemSongBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_song, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemSongBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.albumCover;
      ImageView albumCover = ViewBindings.findChildViewById(rootView, id);
      if (albumCover == null) {
        break missingId;
      }

      id = R.id.albumCoverCard;
      CardView albumCoverCard = ViewBindings.findChildViewById(rootView, id);
      if (albumCoverCard == null) {
        break missingId;
      }

      id = R.id.artistName;
      TextView artistName = ViewBindings.findChildViewById(rootView, id);
      if (artistName == null) {
        break missingId;
      }

      id = R.id.duration;
      TextView duration = ViewBindings.findChildViewById(rootView, id);
      if (duration == null) {
        break missingId;
      }

      id = R.id.favoriteIcon;
      ImageView favoriteIcon = ViewBindings.findChildViewById(rootView, id);
      if (favoriteIcon == null) {
        break missingId;
      }

      id = R.id.songInfoLayout;
      LinearLayout songInfoLayout = ViewBindings.findChildViewById(rootView, id);
      if (songInfoLayout == null) {
        break missingId;
      }

      id = R.id.songTitle;
      TextView songTitle = ViewBindings.findChildViewById(rootView, id);
      if (songTitle == null) {
        break missingId;
      }

      return new ItemSongBinding((ConstraintLayout) rootView, albumCover, albumCoverCard,
          artistName, duration, favoriteIcon, songInfoLayout, songTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
