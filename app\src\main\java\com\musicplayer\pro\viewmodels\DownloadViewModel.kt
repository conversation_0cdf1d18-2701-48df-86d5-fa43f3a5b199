package com.musicplayer.pro.viewmodels

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.musicplayer.pro.models.Download
import com.musicplayer.pro.models.DownloadStatus
import com.musicplayer.pro.managers.DownloadManager
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import java.util.*

/**
 * ViewModel للتحميلات - مطابق لـ DownloadManager في Python
 */
class DownloadViewModel(application: Application) : AndroidViewModel(application) {
    
    private val downloadManager = DownloadManager(application)
    
    // البيانات المباشرة
    private val _downloads = MutableLiveData<List<Download>>()
    val downloads: LiveData<List<Download>> = _downloads
    
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    private val _errorMessage = MutableLiveData<String?>()
    val errorMessage: LiveData<String?> = _errorMessage
    
    init {
        _isLoading.value = false
        loadDownloads()
        startPeriodicUpdates()
    }

    /**
     * بدء التحديثات الدورية
     */
    private fun startPeriodicUpdates() {
        viewModelScope.launch {
            while (true) {
                delay(1000) // تحديث كل ثانية
                loadDownloads()
            }
        }
    }
    
    /**
     * تحميل قائمة التحميلات
     */
    fun loadDownloads() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val downloadList = downloadManager.getAllDownloads()
                _downloads.value = downloadList
                _errorMessage.value = null
            } catch (e: Exception) {
                _errorMessage.value = e.message
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * بدء تحميل جديد
     */
    fun startDownload(url: String, quality: String, format: String) {
        viewModelScope.launch {
            try {
                val downloadId = UUID.randomUUID().toString()
                val download = Download(
                    id = downloadId,
                    url = url,
                    title = extractTitleFromUrl(url),
                    status = DownloadStatus.PENDING,
                    quality = quality,
                    format = format
                )
                
                downloadManager.startDownload(download)
                loadDownloads() // إعادة تحميل القائمة
                
            } catch (e: Exception) {
                _errorMessage.value = e.message
            }
        }
    }
    
    /**
     * إيقاف تحميل مؤقتاً
     */
    fun pauseDownload(downloadId: String) {
        viewModelScope.launch {
            try {
                downloadManager.pauseDownload(downloadId)
                loadDownloads()
            } catch (e: Exception) {
                _errorMessage.value = e.message
            }
        }
    }
    
    /**
     * استئناف تحميل
     */
    fun resumeDownload(downloadId: String) {
        viewModelScope.launch {
            try {
                downloadManager.resumeDownload(downloadId)
                loadDownloads()
            } catch (e: Exception) {
                _errorMessage.value = e.message
            }
        }
    }
    
    /**
     * إلغاء تحميل
     */
    fun cancelDownload(downloadId: String) {
        viewModelScope.launch {
            try {
                downloadManager.cancelDownload(downloadId)
                loadDownloads()
            } catch (e: Exception) {
                _errorMessage.value = e.message
            }
        }
    }
    
    /**
     * إعادة محاولة تحميل
     */
    fun retryDownload(downloadId: String) {
        viewModelScope.launch {
            try {
                downloadManager.retryDownload(downloadId)
                loadDownloads()
            } catch (e: Exception) {
                _errorMessage.value = e.message
            }
        }
    }
    
    /**
     * حذف تحميل
     */
    fun deleteDownload(downloadId: String) {
        viewModelScope.launch {
            try {
                downloadManager.deleteDownload(downloadId)
                loadDownloads()
            } catch (e: Exception) {
                _errorMessage.value = e.message
            }
        }
    }
    
    /**
     * تشغيل ملف محمل
     */
    fun playDownloadedFile(download: Download) {
        viewModelScope.launch {
            try {
                downloadManager.playDownloadedFile(download)
            } catch (e: Exception) {
                _errorMessage.value = e.message
            }
        }
    }
    
    /**
     * استخراج العنوان من الرابط
     */
    private fun extractTitleFromUrl(url: String): String {
        return try {
            // منطق استخراج العنوان من YouTube أو روابط أخرى
            when {
                url.contains("youtube.com") || url.contains("youtu.be") -> {
                    "YouTube Video"
                }
                url.contains("soundcloud.com") -> {
                    "SoundCloud Track"
                }
                else -> {
                    "Downloaded Media"
                }
            }
        } catch (e: Exception) {
            "Unknown Title"
        }
    }
    
    /**
     * مسح رسالة الخطأ
     */
    fun clearErrorMessage() {
        _errorMessage.value = null
    }
}
