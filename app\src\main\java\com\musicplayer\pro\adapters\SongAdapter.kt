package com.musicplayer.pro.adapters

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.musicplayer.pro.R
import com.musicplayer.pro.models.Song
import com.musicplayer.pro.utils.ImageLoader

/**
 * محول قائمة الأغاني
 */
class SongAdapter(
    private val onSongClick: (Song) -> Unit,
    private val onSongLongClick: (Song) -> Unit,
    private val onFavoriteClick: (Song) -> Unit
) : RecyclerView.Adapter<SongAdapter.SongViewHolder>() {
    
    private var songs: List<Song> = emptyList()
    private var currentSong: Song? = null
    private var isPlaying: Boolean = false
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SongViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_song, parent, false)
        return SongViewHolder(view)
    }
    
    override fun onBindViewHolder(holder: <PERSON>ViewHolder, position: Int) {
        val song = songs[position]
        holder.bind(song)
    }
    
    override fun getItemCount(): Int = songs.size
    
    /**
     * تحديث قائمة الأغاني
     */
    fun updateSongs(newSongs: List<Song>) {
        songs = newSongs
        notifyDataSetChanged()
    }
    
    /**
     * تعيين الأغنية الحالية
     */
    fun setCurrentSong(song: Song?) {
        currentSong = song
        notifyDataSetChanged()
    }
    
    /**
     * تعيين حالة التشغيل
     */
    fun setPlayingState(playing: Boolean) {
        isPlaying = playing
        notifyDataSetChanged()
    }
    
    /**
     * الحصول على قائمة الأغاني
     */
    fun getSongs(): List<Song> = songs
    
    inner class SongViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val titleTextView: TextView = itemView.findViewById(R.id.songTitle)
        private val artistTextView: TextView = itemView.findViewById(R.id.artistName)
        private val durationTextView: TextView = itemView.findViewById(R.id.duration)
        private val albumCoverImageView: ImageView = itemView.findViewById(R.id.albumCover)
        private val favoriteImageView: ImageView = itemView.findViewById(R.id.favoriteIcon)
        
        fun bind(song: Song) {
            titleTextView.text = song.title
            artistTextView.text = song.getArtistOrDefault()
            durationTextView.text = song.getFormattedDuration()

            // تحميل صورة الأغنية
            ImageLoader.loadSongImage(
                context = itemView.context,
                song = song,
                imageView = albumCoverImageView,
                placeholder = R.drawable.default_album_cover
            )

            // تحديث أيقونة المفضلة
            favoriteImageView.setImageResource(
                if (song.isFavorite) R.drawable.ic_favorite_filled
                else R.drawable.ic_favorite_outline
            )
            
            // إعداد المستمعين
            itemView.setOnClickListener { onSongClick(song) }
            itemView.setOnLongClickListener { 
                onSongLongClick(song)
                true
            }
            favoriteImageView.setOnClickListener { onFavoriteClick(song) }
        }
    }
}
