<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_main" modulePackage="com.musicplayer.pro" filePath="app\src\main\res\layout\fragment_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/fragment_main_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="119" endOffset="53"/></Target><Target id="@+id/searchInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="15" startOffset="8" endLine="32" endOffset="63"/></Target><Target id="@+id/searchEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="25" startOffset="12" endLine="30" endOffset="38"/></Target><Target id="@+id/statusText" view="TextView"><Expressions/><location startLine="37" startOffset="4" endLine="49" endOffset="70"/></Target><Target id="@+id/recyclerViewSongs" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="52" startOffset="4" endLine="59" endOffset="44"/></Target><Target id="@+id/emptyStateLayout" view="LinearLayout"><Expressions/><location startLine="62" startOffset="4" endLine="95" endOffset="18"/></Target><Target id="@+id/loadingProgressBar" view="com.google.android.material.progressindicator.LinearProgressIndicator"><Expressions/><location startLine="98" startOffset="4" endLine="105" endOffset="52"/></Target><Target id="@+id/fabShuffle" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="108" startOffset="4" endLine="117" endOffset="33"/></Target></Targets></Layout>