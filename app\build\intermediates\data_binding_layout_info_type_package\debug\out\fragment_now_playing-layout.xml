<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_now_playing" modulePackage="com.musicplayer.pro" filePath="app\src\main\res\layout\fragment_now_playing.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_now_playing_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="256" endOffset="51"/></Target><Target id="@+id/backgroundImage" view="ImageView"><Expressions/><location startLine="10" startOffset="4" endLine="20" endOffset="51"/></Target><Target id="@+id/albumCoverCard" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="23" startOffset="4" endLine="48" endOffset="39"/></Target><Target id="@+id/albumCover" view="ImageView"><Expressions/><location startLine="38" startOffset="8" endLine="46" endOffset="55"/></Target><Target id="@+id/songInfoLayout" view="LinearLayout"><Expressions/><location startLine="51" startOffset="4" endLine="99" endOffset="18"/></Target><Target id="@+id/songTitle" view="TextView"><Expressions/><location startLine="62" startOffset="8" endLine="73" endOffset="37"/></Target><Target id="@+id/artistName" view="TextView"><Expressions/><location startLine="75" startOffset="8" endLine="85" endOffset="38"/></Target><Target id="@+id/albumName" view="TextView"><Expressions/><location startLine="87" startOffset="8" endLine="97" endOffset="37"/></Target><Target id="@+id/progressLayout" view="LinearLayout"><Expressions/><location startLine="102" startOffset="4" endLine="149" endOffset="18"/></Target><Target id="@+id/seekBar" view="SeekBar"><Expressions/><location startLine="112" startOffset="8" endLine="118" endOffset="33"/></Target><Target id="@+id/currentTime" view="TextView"><Expressions/><location startLine="126" startOffset="12" endLine="132" endOffset="35"/></Target><Target id="@+id/totalTime" view="TextView"><Expressions/><location startLine="139" startOffset="12" endLine="145" endOffset="35"/></Target><Target id="@+id/controlsLayout" view="LinearLayout"><Expressions/><location startLine="152" startOffset="4" endLine="211" endOffset="18"/></Target><Target id="@+id/btnShuffle" view="ImageButton"><Expressions/><location startLine="163" startOffset="8" endLine="170" endOffset="50"/></Target><Target id="@+id/btnPrevious" view="ImageButton"><Expressions/><location startLine="172" startOffset="8" endLine="180" endOffset="48"/></Target><Target id="@+id/btnPlayPause" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="182" startOffset="8" endLine="190" endOffset="37"/></Target><Target id="@+id/btnNext" view="ImageButton"><Expressions/><location startLine="192" startOffset="8" endLine="200" endOffset="48"/></Target><Target id="@+id/btnRepeat" view="ImageButton"><Expressions/><location startLine="202" startOffset="8" endLine="209" endOffset="50"/></Target><Target id="@+id/secondaryControlsLayout" view="LinearLayout"><Expressions/><location startLine="214" startOffset="4" endLine="254" endOffset="18"/></Target><Target id="@+id/btnFavorite" view="ImageButton"><Expressions/><location startLine="225" startOffset="8" endLine="232" endOffset="50"/></Target><Target id="@+id/btnPlaylist" view="ImageButton"><Expressions/><location startLine="234" startOffset="8" endLine="242" endOffset="50"/></Target><Target id="@+id/btnShare" view="ImageButton"><Expressions/><location startLine="244" startOffset="8" endLine="252" endOffset="50"/></Target></Targets></Layout>