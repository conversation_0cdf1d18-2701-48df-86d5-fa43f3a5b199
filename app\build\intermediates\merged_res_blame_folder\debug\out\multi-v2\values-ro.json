{"logs": [{"outputFile": "com.musicplayer.pro.app-mergeDebugResources-55:/values-ro/values-ro.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7c108dd56627f30fe94755d1a2faeaf2\\transformed\\core-1.12.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,454,556,665,782", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "148,250,350,449,551,660,777,878"}, "to": {"startLines": "58,59,60,61,62,63,64,186", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4367,4465,4567,4667,4766,4868,4977,14779", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "4460,4562,4662,4761,4863,4972,5089,14875"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e913642d7c50f47db3e63580f9572497\\transformed\\navigation-ui-2.7.6\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,123", "endOffsets": "160,284"}, "to": {"startLines": "180,181", "startColumns": "4,4", "startOffsets": "14168,14278", "endColumns": "109,123", "endOffsets": "14273,14397"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\472040ed58573b5e54239e60b254baef\\transformed\\jetified-media3-ui-1.2.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,582,852,940,1030,1119,1216,1310,1385,1451,1548,1646,1715,1778,1841,1910,2024,2137,2251,2328,2408,2477,2553,2652,2753,2819,2882,2935,2993,3041,3102,3166,3236,3301,3370,3431,3489,3555,3619,3685,3737,3799,3875,3951", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,87,89,88,96,93,74,65,96,97,68,62,62,68,113,112,113,76,79,68,75,98,100,65,62,52,57,47,60,63,69,64,68,60,57,65,63,65,51,61,75,75,56", "endOffsets": "281,577,847,935,1025,1114,1211,1305,1380,1446,1543,1641,1710,1773,1836,1905,2019,2132,2246,2323,2403,2472,2548,2647,2748,2814,2877,2930,2988,3036,3097,3161,3231,3296,3365,3426,3484,3550,3614,3680,3732,3794,3870,3946,4003"}, "to": {"startLines": "2,11,16,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,381,677,5537,5625,5715,5804,5901,5995,6070,6136,6233,6331,6400,6463,6526,6595,6709,6822,6936,7013,7093,7162,7238,7337,7438,7504,8262,8315,8373,8421,8482,8546,8616,8681,8750,8811,8869,8935,8999,9065,9117,9179,9255,9331", "endLines": "10,15,20,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "endColumns": "17,12,12,87,89,88,96,93,74,65,96,97,68,62,62,68,113,112,113,76,79,68,75,98,100,65,62,52,57,47,60,63,69,64,68,60,57,65,63,65,51,61,75,75,56", "endOffsets": "376,672,942,5620,5710,5799,5896,5990,6065,6131,6228,6326,6395,6458,6521,6590,6704,6817,6931,7008,7088,7157,7233,7332,7433,7499,7562,8310,8368,8416,8477,8541,8611,8676,8745,8806,8864,8930,8994,9060,9112,9174,9250,9326,9383"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d0962e3cd95d785309666b77b5473090\\transformed\\appcompat-1.6.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,330,443,527,631,752,837,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1855,1938,2050,2158,2258,2372,2478,2584,2748,2851", "endColumns": "120,103,112,83,103,120,84,79,90,92,94,93,99,92,94,93,90,91,82,111,107,99,113,105,105,163,102,83", "endOffsets": "221,325,438,522,626,747,832,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1850,1933,2045,2153,2253,2367,2473,2579,2743,2846,2930"}, "to": {"startLines": "26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,185", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1168,1289,1393,1506,1590,1694,1815,1900,1980,2071,2164,2259,2353,2453,2546,2641,2735,2826,2918,3001,3113,3221,3321,3435,3541,3647,3811,14695", "endColumns": "120,103,112,83,103,120,84,79,90,92,94,93,99,92,94,93,90,91,82,111,107,99,113,105,105,163,102,83", "endOffsets": "1284,1388,1501,1585,1689,1810,1895,1975,2066,2159,2254,2348,2448,2541,2636,2730,2821,2913,2996,3108,3216,3316,3430,3536,3642,3806,3909,14774"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2e081d70774c49b9b7dc6950692deb52\\transformed\\material-1.11.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,321,413,501,588,684,774,875,996,1080,1146,1241,1315,1375,1459,1521,1587,1645,1718,1781,1837,1956,2013,2074,2130,2204,2349,2435,2519,2652,2734,2817,2963,3053,3133,3188,3239,3305,3378,3456,3544,3629,3700,3777,3851,3923,4029,4120,4194,4289,4387,4461,4541,4642,4695,4781,4847,4936,5026,5088,5152,5215,5289,5401,5511,5621,5726,5785,5840", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "endColumns": "12,91,87,86,95,89,100,120,83,65,94,73,59,83,61,65,57,72,62,55,118,56,60,55,73,144,85,83,132,81,82,145,89,79,54,50,65,72,77,87,84,70,76,73,71,105,90,73,94,97,73,79,100,52,85,65,88,89,61,63,62,73,111,109,109,104,58,54,78", "endOffsets": "316,408,496,583,679,769,870,991,1075,1141,1236,1310,1370,1454,1516,1582,1640,1713,1776,1832,1951,2008,2069,2125,2199,2344,2430,2514,2647,2729,2812,2958,3048,3128,3183,3234,3300,3373,3451,3539,3624,3695,3772,3846,3918,4024,4115,4189,4284,4382,4456,4536,4637,4690,4776,4842,4931,5021,5083,5147,5210,5284,5396,5506,5616,5721,5780,5835,5914"}, "to": {"startLines": "21,53,54,55,56,57,65,66,67,69,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,183", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "947,3914,4006,4094,4181,4277,5094,5195,5316,5471,9474,9569,9643,9703,9787,9849,9915,9973,10046,10109,10165,10284,10341,10402,10458,10532,10677,10763,10847,10980,11062,11145,11291,11381,11461,11516,11567,11633,11706,11784,11872,11957,12028,12105,12179,12251,12357,12448,12522,12617,12715,12789,12869,12970,13023,13109,13175,13264,13354,13416,13480,13543,13617,13729,13839,13949,14054,14113,14477", "endLines": "25,53,54,55,56,57,65,66,67,69,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,183", "endColumns": "12,91,87,86,95,89,100,120,83,65,94,73,59,83,61,65,57,72,62,55,118,56,60,55,73,144,85,83,132,81,82,145,89,79,54,50,65,72,77,87,84,70,76,73,71,105,90,73,94,97,73,79,100,52,85,65,88,89,61,63,62,73,111,109,109,104,58,54,78", "endOffsets": "1163,4001,4089,4176,4272,4362,5190,5311,5395,5532,9564,9638,9698,9782,9844,9910,9968,10041,10104,10160,10279,10336,10397,10453,10527,10672,10758,10842,10975,11057,11140,11286,11376,11456,11511,11562,11628,11701,11779,11867,11952,12023,12100,12174,12246,12352,12443,12517,12612,12710,12784,12864,12965,13018,13104,13170,13259,13349,13411,13475,13538,13612,13724,13834,13944,14049,14108,14163,14551"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\210f3162f1672bd532c02d8af1bd50c1\\transformed\\preference-1.2.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,262,337,476,645,732", "endColumns": "70,85,74,138,168,86,80", "endOffsets": "171,257,332,471,640,727,808"}, "to": {"startLines": "68,121,182,184,187,188,189", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5400,9388,14402,14556,14880,15049,15136", "endColumns": "70,85,74,138,168,86,80", "endOffsets": "5466,9469,14472,14690,15044,15131,15212"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f0c4d03d60aaed2968a93a146657af49\\transformed\\jetified-media3-exoplayer-1.2.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,198,263,335,413,493,583,676", "endColumns": "80,61,64,71,77,79,89,92,73", "endOffsets": "131,193,258,330,408,488,578,671,745"}, "to": {"startLines": "94,95,96,97,98,99,100,101,102", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7567,7648,7710,7775,7847,7925,8005,8095,8188", "endColumns": "80,61,64,71,77,79,89,92,73", "endOffsets": "7643,7705,7770,7842,7920,8000,8090,8183,8257"}}]}]}