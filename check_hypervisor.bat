@echo off
echo تحقق من حالة Hypervisor...
echo.

echo === معلومات المعالج ===
wmic cpu get name,virtualizationfirmwareenabled /format:table

echo.
echo === حالة Hyper-V ===
bcdedit /enum | findstr hypervisorlaunchtype

echo.
echo === تحقق من Windows Hypervisor Platform ===
dism /online /get-featureinfo /featurename:HypervisorPlatform

echo.
echo === اختبار Android Emulator ===
if exist "E:\sdk\emulator\emulator.exe" (
    echo Android Emulator موجود
    "E:\sdk\emulator\emulator.exe" -accel-check
) else (
    echo Android Emulator غير موجود
)

pause
