package com.musicplayer.pro.models

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * نموذج بيانات الأغنية
 */
@Parcelize
data class Song(
    val id: Long = 0,
    val title: String = "",
    val artist: String = "",
    val album: String = "",
    val duration: Long = 0,
    val path: String = "",
    val albumArt: String? = null,
    val size: Long = 0,
    val dateAdded: Long = 0,
    val dateModified: Long = 0,
    val mimeType: String = "",
    val bitrate: Int = 0,
    val sampleRate: Int = 0,
    val genre: String = "",
    val year: Int = 0,
    val track: Int = 0,
    val composer: String = "",
    val albumArtist: String = "",
    val isDownloaded: Boolean = false,
    val downloadUrl: String? = null,
    val isFavorite: Boolean = false,
    val playCount: Int = 0,
    val lastPlayed: Long = 0
) : Parcelable {
    
    /**
     * الحصول على اسم الفنان أو قيمة افتراضية
     */
    fun getArtistOrDefault(): String {
        return if (artist.isNotBlank()) artist else "Unknown Artist"
    }
    
    /**
     * الحصول على اسم الألبوم أو قيمة افتراضية
     */
    fun getAlbumOrDefault(): String {
        return if (album.isNotBlank()) album else "Unknown Album"
    }
    
    /**
     * تحويل المدة إلى تنسيق قابل للقراءة
     */
    fun getFormattedDuration(): String {
        val minutes = duration / 1000 / 60
        val seconds = (duration / 1000) % 60
        return String.format("%d:%02d", minutes, seconds)
    }
    
    /**
     * تحويل الحجم إلى تنسيق قابل للقراءة
     */
    fun getFormattedSize(): String {
        return when {
            size < 1024 -> "$size B"
            size < 1024 * 1024 -> "${size / 1024} KB"
            else -> "${size / (1024 * 1024)} MB"
        }
    }
    
    /**
     * فحص ما إذا كان الملف موجود
     */
    fun exists(): Boolean {
        return java.io.File(path).exists()
    }
    
    /**
     * الحصول على امتداد الملف
     */
    fun getFileExtension(): String {
        return path.substringAfterLast('.', "")
    }
    
    /**
     * فحص ما إذا كان الملف صوتي
     */
    fun isAudioFile(): Boolean {
        val audioExtensions = listOf("mp3", "wav", "flac", "m4a", "aac", "ogg", "wma")
        return audioExtensions.contains(getFileExtension().lowercase())
    }
}
