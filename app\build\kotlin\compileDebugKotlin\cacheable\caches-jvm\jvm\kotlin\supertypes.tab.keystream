*com.musicplayer.pro.MusicPlayerApplication&com.musicplayer.pro.SimpleMainActivity com.musicplayer.pro.MainActivity1com.musicplayer.pro.MainActivity.MainPagerAdapter(com.musicplayer.pro.adapters.SongAdapter7com.musicplayer.pro.adapters.SongAdapter.SongViewHolder.com.musicplayer.pro.fragments.DownloadFragment*com.musicplayer.pro.fragments.MainFragment0com.musicplayer.pro.fragments.NowPlayingFragment#com.musicplayer.pro.models.Playlist'com.musicplayer.pro.models.PlaylistType#com.musicplayer.pro.models.Download)com.musicplayer.pro.models.DownloadStatus%com.musicplayer.pro.models.RepeatModecom.musicplayer.pro.models.Song)com.musicplayer.pro.services.MusicService5com.musicplayer.pro.services.MusicService.MusicBinder-com.musicplayer.pro.viewmodels.MusicViewModel9com.musicplayer.pro.databinding.FragmentNowPlayingBinding/com.musicplayer.pro.databinding.ItemSongBinding3com.musicplayer.pro.databinding.ActivityMainBinding5com.musicplayer.pro.databinding.BottomMusicBarBinding7com.musicplayer.pro.databinding.FragmentDownloadBinding3com.musicplayer.pro.databinding.FragmentMainBinding,com.musicplayer.pro.adapters.DownloadAdapter?com.musicplayer.pro.adapters.DownloadAdapter.DownloadViewHolder0com.musicplayer.pro.viewmodels.DownloadViewModel8com.musicplayer.pro.databinding.DialogAddDownloadBinding3com.musicplayer.pro.databinding.ItemDownloadBinding$com.musicplayer.pro.SettingsActivity5com.musicplayer.pro.SettingsActivity.SettingsFragment&com.musicplayer.pro.managers.CacheType7com.musicplayer.pro.databinding.ActivitySettingsBinding%com.musicplayer.pro.EqualizerActivity-com.musicplayer.pro.adapters.EqualizerAdapter<com.musicplayer.pro.adapters.EqualizerAdapter.BandViewHolder8com.musicplayer.pro.databinding.ActivityEqualizerBinding8com.musicplayer.pro.databinding.ItemEqualizerBandBinding                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         