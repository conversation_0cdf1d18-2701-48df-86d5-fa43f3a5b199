package com.musicplayer.pro.fragments

import android.content.ClipboardManager
import android.content.Context
import android.os.Bundle
import android.view.*
import android.widget.*
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.floatingactionbutton.FloatingActionButton
import com.google.android.material.textfield.TextInputEditText
import com.google.android.material.textfield.TextInputLayout
import com.musicplayer.pro.R
import com.musicplayer.pro.adapters.DownloadAdapter
import com.musicplayer.pro.models.Download
import com.musicplayer.pro.models.DownloadStatus
import com.musicplayer.pro.viewmodels.DownloadViewModel
import com.musicplayer.pro.utils.UrlValidator

/**
 * شاشة التحميلات
 * تدير تحميل الأغاني من الإنترنت وعرض قائمة التحميلات
 */
class DownloadFragment : Fragment() {
    
    companion object {
        private const val TAG = "DownloadFragment"
    }
    
    // ViewModel
    private lateinit var viewModel: DownloadViewModel
    
    // UI Components
    private lateinit var recyclerView: RecyclerView
    private lateinit var emptyStateLayout: LinearLayout
    private lateinit var emptyStateTextView: TextView
    private lateinit var fabAddDownload: FloatingActionButton
    
    // Adapter
    private lateinit var downloadAdapter: DownloadAdapter
    
    // Download Dialog
    private var downloadDialog: BottomSheetDialog? = null
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_download, container, false)
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        // تهيئة ViewModel
        viewModel = ViewModelProvider(this)[DownloadViewModel::class.java]
        
        // تهيئة UI
        initializeViews(view)
        setupRecyclerView()
        setupFab()
        
        // مراقبة البيانات
        observeData()
        
        // تحميل قائمة التحميلات
        viewModel.loadDownloads()
        
        // التحقق من وجود رابط في الحافظة
        checkClipboardForUrl()
    }
    
    /**
     * تهيئة العناصر
     */
    private fun initializeViews(view: View) {
        recyclerView = view.findViewById(R.id.recyclerView)
        emptyStateLayout = view.findViewById(R.id.emptyStateLayout)
        emptyStateTextView = view.findViewById(R.id.emptyStateTextView)
        fabAddDownload = view.findViewById(R.id.fabAddDownload)
    }
    
    /**
     * إعداد RecyclerView
     */
    private fun setupRecyclerView() {
        downloadAdapter = DownloadAdapter(
            onDownloadClick = { download -> onDownloadClicked(download) },
            onPauseClick = { download -> viewModel.pauseDownload(download.id) },
            onResumeClick = { download -> viewModel.resumeDownload(download.id) },
            onCancelClick = { download -> viewModel.cancelDownload(download.id) },
            onRetryClick = { download -> viewModel.retryDownload(download.id) },
            onDeleteClick = { download -> showDeleteConfirmation(download) }
        )
        
        recyclerView.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = downloadAdapter
        }
    }
    
    /**
     * إعداد زر الإضافة
     */
    private fun setupFab() {
        fabAddDownload.setOnClickListener {
            showAddDownloadDialog()
        }
    }
    
    /**
     * مراقبة البيانات
     */
    private fun observeData() {
        // مراقبة قائمة التحميلات
        viewModel.downloads.observe(viewLifecycleOwner) { downloads ->
            updateDownloadsList(downloads)
        }
        
        // مراقبة حالة التحميل
        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            updateLoadingState(isLoading)
        }
        
        // مراقبة الرسائل
        viewModel.message.observe(viewLifecycleOwner) { message ->
            if (message.isNotEmpty()) {
                showMessage(message)
            }
        }
        
        // مراقبة تقدم التحميلات
        viewModel.downloadProgress.observe(viewLifecycleOwner) { progressMap ->
            downloadAdapter.updateProgress(progressMap)
        }
    }
    
    /**
     * تحديث قائمة التحميلات
     */
    private fun updateDownloadsList(downloads: List<Download>) {
        if (downloads.isEmpty()) {
            showEmptyState()
        } else {
            hideEmptyState()
            downloadAdapter.updateDownloads(downloads)
        }
    }
    
    /**
     * عرض الحالة الفارغة
     */
    private fun showEmptyState() {
        recyclerView.visibility = View.GONE
        emptyStateLayout.visibility = View.VISIBLE
        emptyStateTextView.text = getString(R.string.no_downloads_message)
    }
    
    /**
     * إخفاء الحالة الفارغة
     */
    private fun hideEmptyState() {
        recyclerView.visibility = View.VISIBLE
        emptyStateLayout.visibility = View.GONE
    }
    
    /**
     * تحديث حالة التحميل
     */
    private fun updateLoadingState(isLoading: Boolean) {
        // يمكن إضافة مؤشر تحميل هنا
    }
    
    /**
     * عرض رسالة
     */
    private fun showMessage(message: String) {
        Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show()
    }
    
    /**
     * التحقق من وجود رابط في الحافظة
     */
    private fun checkClipboardForUrl() {
        val clipboardManager = requireContext().getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        val clipData = clipboardManager.primaryClip
        
        if (clipData != null && clipData.itemCount > 0) {
            val clipText = clipData.getItemAt(0).text?.toString()
            
            if (!clipText.isNullOrEmpty() && UrlValidator.isValidUrl(clipText)) {
                showClipboardUrlDialog(clipText)
            }
        }
    }
    
    /**
     * عرض حوار رابط الحافظة
     */
    private fun showClipboardUrlDialog(url: String) {
        androidx.appcompat.app.AlertDialog.Builder(requireContext())
            .setTitle(getString(R.string.url_found_in_clipboard))
            .setMessage(getString(R.string.download_from_clipboard_message, url))
            .setPositiveButton(getString(R.string.download)) { _, _ ->
                showAddDownloadDialog(url)
            }
            .setNegativeButton(getString(R.string.cancel), null)
            .show()
    }
    
    /**
     * عرض حوار إضافة تحميل
     */
    private fun showAddDownloadDialog(prefilledUrl: String = "") {
        downloadDialog = BottomSheetDialog(requireContext())
        val view = layoutInflater.inflate(R.layout.dialog_add_download, null)
        
        // العناصر
        val urlInputLayout = view.findViewById<TextInputLayout>(R.id.urlInputLayout)
        val urlEditText = view.findViewById<TextInputEditText>(R.id.urlEditText)
        val qualitySpinner = view.findViewById<Spinner>(R.id.qualitySpinner)
        val formatSpinner = view.findViewById<Spinner>(R.id.formatSpinner)
        val downloadButton = view.findViewById<Button>(R.id.downloadButton)
        val cancelButton = view.findViewById<Button>(R.id.cancelButton)
        val pasteButton = view.findViewById<ImageButton>(R.id.pasteButton)
        
        // تعبئة الرابط المسبق
        if (prefilledUrl.isNotEmpty()) {
            urlEditText.setText(prefilledUrl)
        }
        
        // إعداد Spinners
        setupQualitySpinner(qualitySpinner)
        setupFormatSpinner(formatSpinner)
        
        // زر اللصق
        pasteButton.setOnClickListener {
            val clipboardManager = requireContext().getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            val clipData = clipboardManager.primaryClip
            
            if (clipData != null && clipData.itemCount > 0) {
                val clipText = clipData.getItemAt(0).text?.toString()
                if (!clipText.isNullOrEmpty()) {
                    urlEditText.setText(clipText)
                }
            }
        }
        
        // زر التحميل
        downloadButton.setOnClickListener {
            val url = urlEditText.text?.toString()?.trim()
            
            if (url.isNullOrEmpty()) {
                urlInputLayout.error = getString(R.string.url_required)
                return@setOnClickListener
            }
            
            if (!UrlValidator.isValidUrl(url)) {
                urlInputLayout.error = getString(R.string.invalid_url)
                return@setOnClickListener
            }
            
            val quality = qualitySpinner.selectedItem.toString()
            val format = formatSpinner.selectedItem.toString()
            
            // بدء التحميل
            viewModel.startDownload(url, quality, format)
            downloadDialog?.dismiss()
        }
        
        // زر الإلغاء
        cancelButton.setOnClickListener {
            downloadDialog?.dismiss()
        }
        
        // مراقبة تغيير النص لإزالة الخطأ
        urlEditText.setOnTextChangedListener { text ->
            if (!text.isNullOrEmpty()) {
                urlInputLayout.error = null
            }
        }
        
        downloadDialog?.setContentView(view)
        downloadDialog?.show()
    }
    
    /**
     * إعداد قائمة الجودة
     */
    private fun setupQualitySpinner(spinner: Spinner) {
        val qualities = arrayOf(
            getString(R.string.quality_best),
            getString(R.string.quality_high),
            getString(R.string.quality_medium),
            getString(R.string.quality_low)
        )
        
        val adapter = ArrayAdapter(requireContext(), android.R.layout.simple_spinner_item, qualities)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        spinner.adapter = adapter
    }
    
    /**
     * إعداد قائمة التنسيق
     */
    private fun setupFormatSpinner(spinner: Spinner) {
        val formats = arrayOf("MP3", "MP4", "M4A", "WEBM")
        
        val adapter = ArrayAdapter(requireContext(), android.R.layout.simple_spinner_item, formats)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        spinner.adapter = adapter
    }
    
    /**
     * عند النقر على تحميل
     */
    private fun onDownloadClicked(download: Download) {
        when (download.status) {
            DownloadStatus.COMPLETED -> {
                // تشغيل الملف المحمل
                viewModel.playDownloadedFile(download)
            }
            DownloadStatus.FAILED -> {
                // عرض تفاصيل الخطأ
                showErrorDetails(download)
            }
            else -> {
                // عرض تفاصيل التحميل
                showDownloadDetails(download)
            }
        }
    }
    
    /**
     * عرض تفاصيل الخطأ
     */
    private fun showErrorDetails(download: Download) {
        androidx.appcompat.app.AlertDialog.Builder(requireContext())
            .setTitle(getString(R.string.download_error))
            .setMessage(download.error.ifEmpty { getString(R.string.unknown_error) })
            .setPositiveButton(getString(R.string.retry)) { _, _ ->
                viewModel.retryDownload(download.id)
            }
            .setNegativeButton(getString(R.string.cancel), null)
            .show()
    }
    
    /**
     * عرض تفاصيل التحميل
     */
    private fun showDownloadDetails(download: Download) {
        val details = buildString {
            appendLine("${getString(R.string.title)}: ${download.title}")
            appendLine("${getString(R.string.artist)}: ${download.artist}")
            appendLine("${getString(R.string.quality)}: ${download.quality}")
            appendLine("${getString(R.string.format)}: ${download.format}")
            appendLine("${getString(R.string.size)}: ${download.getFormattedFileSize()}")
            appendLine("${getString(R.string.progress)}: ${download.getProgress()}%")
            appendLine("${getString(R.string.status)}: ${getStatusText(download.status)}")
        }
        
        androidx.appcompat.app.AlertDialog.Builder(requireContext())
            .setTitle(getString(R.string.download_details))
            .setMessage(details)
            .setPositiveButton(getString(R.string.ok), null)
            .show()
    }
    
    /**
     * الحصول على نص الحالة
     */
    private fun getStatusText(status: DownloadStatus): String {
        return when (status) {
            DownloadStatus.PENDING -> getString(R.string.status_pending)
            DownloadStatus.DOWNLOADING -> getString(R.string.status_downloading)
            DownloadStatus.PAUSED -> getString(R.string.status_paused)
            DownloadStatus.COMPLETED -> getString(R.string.status_completed)
            DownloadStatus.FAILED -> getString(R.string.status_failed)
            DownloadStatus.CANCELLED -> getString(R.string.status_cancelled)
        }
    }
    
    /**
     * عرض تأكيد الحذف
     */
    private fun showDeleteConfirmation(download: Download) {
        androidx.appcompat.app.AlertDialog.Builder(requireContext())
            .setTitle(getString(R.string.delete_download))
            .setMessage(getString(R.string.delete_download_message, download.title))
            .setPositiveButton(getString(R.string.delete)) { _, _ ->
                viewModel.deleteDownload(download.id)
            }
            .setNegativeButton(getString(R.string.cancel), null)
            .show()
    }
    
    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        inflater.inflate(R.menu.download_menu, menu)
        super.onCreateOptionsMenu(menu, inflater)
    }
    
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_clear_completed -> {
                viewModel.clearCompletedDownloads()
                true
            }
            R.id.action_pause_all -> {
                viewModel.pauseAllDownloads()
                true
            }
            R.id.action_resume_all -> {
                viewModel.resumeAllDownloads()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setHasOptionsMenu(true)
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        downloadDialog?.dismiss()
    }
}
