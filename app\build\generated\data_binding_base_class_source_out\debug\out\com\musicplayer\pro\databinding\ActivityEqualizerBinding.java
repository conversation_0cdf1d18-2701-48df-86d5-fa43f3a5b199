// Generated by view binder compiler. Do not edit!
package com.musicplayer.pro.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.SeekBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.musicplayer.pro.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityEqualizerBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final TextView bassBoostLabel;

  @NonNull
  public final SeekBar bassBoostSeekBar;

  @NonNull
  public final LinearLayout effectsControlsLayout;

  @NonNull
  public final TextView equalizerTitle;

  @NonNull
  public final RecyclerView recyclerViewEqualizer;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView virtualizerLabel;

  @NonNull
  public final SeekBar virtualizerSeekBar;

  private ActivityEqualizerBinding(@NonNull ConstraintLayout rootView,
      @NonNull TextView bassBoostLabel, @NonNull SeekBar bassBoostSeekBar,
      @NonNull LinearLayout effectsControlsLayout, @NonNull TextView equalizerTitle,
      @NonNull RecyclerView recyclerViewEqualizer, @NonNull Toolbar toolbar,
      @NonNull TextView virtualizerLabel, @NonNull SeekBar virtualizerSeekBar) {
    this.rootView = rootView;
    this.bassBoostLabel = bassBoostLabel;
    this.bassBoostSeekBar = bassBoostSeekBar;
    this.effectsControlsLayout = effectsControlsLayout;
    this.equalizerTitle = equalizerTitle;
    this.recyclerViewEqualizer = recyclerViewEqualizer;
    this.toolbar = toolbar;
    this.virtualizerLabel = virtualizerLabel;
    this.virtualizerSeekBar = virtualizerSeekBar;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityEqualizerBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityEqualizerBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_equalizer, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityEqualizerBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.bassBoostLabel;
      TextView bassBoostLabel = ViewBindings.findChildViewById(rootView, id);
      if (bassBoostLabel == null) {
        break missingId;
      }

      id = R.id.bassBoostSeekBar;
      SeekBar bassBoostSeekBar = ViewBindings.findChildViewById(rootView, id);
      if (bassBoostSeekBar == null) {
        break missingId;
      }

      id = R.id.effectsControlsLayout;
      LinearLayout effectsControlsLayout = ViewBindings.findChildViewById(rootView, id);
      if (effectsControlsLayout == null) {
        break missingId;
      }

      id = R.id.equalizerTitle;
      TextView equalizerTitle = ViewBindings.findChildViewById(rootView, id);
      if (equalizerTitle == null) {
        break missingId;
      }

      id = R.id.recyclerViewEqualizer;
      RecyclerView recyclerViewEqualizer = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewEqualizer == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.virtualizerLabel;
      TextView virtualizerLabel = ViewBindings.findChildViewById(rootView, id);
      if (virtualizerLabel == null) {
        break missingId;
      }

      id = R.id.virtualizerSeekBar;
      SeekBar virtualizerSeekBar = ViewBindings.findChildViewById(rootView, id);
      if (virtualizerSeekBar == null) {
        break missingId;
      }

      return new ActivityEqualizerBinding((ConstraintLayout) rootView, bassBoostLabel,
          bassBoostSeekBar, effectsControlsLayout, equalizerTitle, recyclerViewEqualizer, toolbar,
          virtualizerLabel, virtualizerSeekBar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
