<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary">

    <!-- شريط البحث -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/surface_primary"
        app:elevation="0dp">

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/searchInputLayout"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:hint="@string/search_songs"
            app:startIconDrawable="@drawable/ic_search"
            app:endIconMode="clear_text">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/searchEditText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text"
                android:maxLines="1" />

        </com.google.android.material.textfield.TextInputLayout>

    </com.google.android.material.appbar.AppBarLayout>

    <!-- نص حالة الفحص -->
    <TextView
        android:id="@+id/statusText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:background="@color/accent_primary"
        android:gravity="center"
        android:padding="12dp"
        android:text="جاري البحث عن الأغاني..."
        android:textColor="@android:color/white"
        android:textSize="14sp"
        android:visibility="gone"
        app:layout_behavior="@string/appbar_scrolling_view_behavior" />

    <!-- قائمة الأغاني -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewSongs"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipToPadding="false"
        android:paddingBottom="16dp"
        app:layout_behavior="@string/appbar_scrolling_view_behavior"
        tools:listitem="@layout/item_song" />

    <!-- رسالة عدم وجود أغاني -->
    <LinearLayout
        android:id="@+id/emptyStateLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        tools:visibility="visible">

        <ImageView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:alpha="0.5"
            android:src="@drawable/ic_music_library"
            android:tint="@color/text_secondary" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="@string/no_songs_found"
            android:textColor="@color/text_secondary"
            android:textSize="18sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="@string/add_songs_to_get_started"
            android:textColor="@color/text_tertiary"
            android:textSize="14sp" />

    </LinearLayout>

    <!-- شريط التحميل -->
    <com.google.android.material.progressindicator.LinearProgressIndicator
        android:id="@+id/loadingProgressBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="top"
        android:indeterminate="true"
        android:visibility="gone"
        app:indicatorColor="@color/accent_primary" />

    <!-- زر التشغيل العشوائي -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabShuffle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:contentDescription="@string/shuffle_all"
        app:srcCompat="@drawable/ic_shuffle_on"
        app:backgroundTint="@color/accent_primary"
        app:tint="@color/white" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
