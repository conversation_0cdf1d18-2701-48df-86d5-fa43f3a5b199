<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary"
    android:padding="24dp">

    <!-- خلفية ضبابية -->
    <ImageView
        android:id="@+id/backgroundImage"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:alpha="0.1"
        android:src="@drawable/default_album_cover"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- صورة غلاف الألبوم -->
    <androidx.cardview.widget.CardView
        android:id="@+id/albumCoverCard"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="32dp"
        android:layout_marginBottom="32dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="8dp"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_max="320dp"
        app:layout_constraintBottom_toTopOf="@+id/songInfoLayout">

        <ImageView
            android:id="@+id/albumCover"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:src="@drawable/default_album_cover"
            android:contentDescription="@string/album_cover"
            android:background="@color/surface_secondary"
            tools:src="@drawable/default_album_cover" />

    </androidx.cardview.widget.CardView>

    <!-- معلومات الأغنية -->
    <LinearLayout
        android:id="@+id/songInfoLayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/progressLayout">

        <TextView
            android:id="@+id/songTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="marquee"
            android:gravity="center"
            android:marqueeRepeatLimit="marquee_forever"
            android:singleLine="true"
            android:textColor="@color/text_primary"
            android:textSize="24sp"
            android:textStyle="bold"
            tools:text="Song Title" />

        <TextView
            android:id="@+id/artistName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:ellipsize="end"
            android:gravity="center"
            android:singleLine="true"
            android:textColor="@color/text_secondary"
            android:textSize="18sp"
            tools:text="Artist Name" />

        <TextView
            android:id="@+id/albumName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:ellipsize="end"
            android:gravity="center"
            android:singleLine="true"
            android:textColor="@color/text_tertiary"
            android:textSize="16sp"
            tools:text="Album Name" />

    </LinearLayout>

    <!-- شريط التقدم والوقت -->
    <LinearLayout
        android:id="@+id/progressLayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/controlsLayout">

        <SeekBar
            android:id="@+id/seekBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:progressTint="@color/accent_primary"
            android:thumbTint="@color/accent_primary"
            tools:progress="30" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/currentTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_secondary"
                android:textSize="14sp"
                tools:text="1:23" />

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />

            <TextView
                android:id="@+id/totalTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_secondary"
                android:textSize="14sp"
                tools:text="3:45" />

        </LinearLayout>

    </LinearLayout>

    <!-- أزرار التحكم -->
    <LinearLayout
        android:id="@+id/controlsLayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/secondaryControlsLayout">

        <ImageButton
            android:id="@+id/btnShuffle"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/shuffle"
            android:src="@drawable/ic_shuffle_off"
            android:tint="@color/text_secondary" />

        <ImageButton
            android:id="@+id/btnPrevious"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_marginHorizontal="16dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/previous"
            android:src="@drawable/ic_skip_previous"
            android:tint="@color/text_primary" />

        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/btnPlayPause"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="8dp"
            android:contentDescription="@string/play_pause"
            app:backgroundTint="@color/accent_primary"
            app:srcCompat="@drawable/ic_play"
            app:tint="@color/white" />

        <ImageButton
            android:id="@+id/btnNext"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_marginHorizontal="16dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/next"
            android:src="@drawable/ic_skip_next"
            android:tint="@color/text_primary" />

        <ImageButton
            android:id="@+id/btnRepeat"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/repeat"
            android:src="@drawable/ic_repeat_off"
            android:tint="@color/text_secondary" />

    </LinearLayout>

    <!-- أزرار ثانوية -->
    <LinearLayout
        android:id="@+id/secondaryControlsLayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <ImageButton
            android:id="@+id/btnFavorite"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/favorite"
            android:src="@drawable/ic_favorite_outline"
            android:tint="@color/text_secondary" />

        <ImageButton
            android:id="@+id/btnPlaylist"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginStart="32dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/add_to_playlist"
            android:src="@drawable/ic_playlist_add"
            android:tint="@color/text_secondary" />

        <ImageButton
            android:id="@+id/btnShare"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginStart="32dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/share"
            android:src="@drawable/ic_share"
            android:tint="@color/text_secondary" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
