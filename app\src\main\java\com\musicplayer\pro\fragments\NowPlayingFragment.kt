package com.musicplayer.pro.fragments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.SeekBar
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.musicplayer.pro.R
import com.musicplayer.pro.models.Song
import com.musicplayer.pro.viewmodels.MusicViewModel
import com.musicplayer.pro.utils.ImageLoader

/**
 * شاشة التشغيل الحالي
 * مطابقة لـ NowPlayingScreen في Python
 */
class NowPlayingFragment : Fragment() {
    
    // UI Components
    private lateinit var backgroundImage: ImageView
    private lateinit var albumCover: ImageView
    private lateinit var songTitle: TextView
    private lateinit var artistName: TextView
    private lateinit var albumName: TextView
    private lateinit var currentTime: TextView
    private lateinit var totalTime: TextView
    private lateinit var seekBar: SeekBar
    private lateinit var btnPrevious: ImageButton
    private lateinit var btnPlayPause: ImageButton
    private lateinit var btnNext: ImageButton
    private lateinit var btnShuffle: ImageButton
    private lateinit var btnRepeat: ImageButton
    private lateinit var btnFavorite: ImageButton
    
    private lateinit var viewModel: MusicViewModel
    private var isUserSeeking = false
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_now_playing, container, false)
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        // تهيئة ViewModel
        viewModel = ViewModelProvider(requireActivity())[MusicViewModel::class.java]
        
        // تهيئة UI
        initializeUI(view)
        
        // إعداد المراقبين
        setupObservers()
        
        // إعداد المستمعين
        setupListeners()
    }
    
    /**
     * تهيئة واجهة المستخدم
     */
    private fun initializeUI(view: View) {
        backgroundImage = view.findViewById(R.id.backgroundImage)
        albumCover = view.findViewById(R.id.albumCover)
        songTitle = view.findViewById(R.id.songTitle)
        artistName = view.findViewById(R.id.artistName)
        albumName = view.findViewById(R.id.albumName)
        currentTime = view.findViewById(R.id.currentTime)
        totalTime = view.findViewById(R.id.totalTime)
        seekBar = view.findViewById(R.id.seekBar)
        btnPrevious = view.findViewById(R.id.btnPrevious)
        btnPlayPause = view.findViewById(R.id.btnPlayPause)
        btnNext = view.findViewById(R.id.btnNext)
        btnShuffle = view.findViewById(R.id.btnShuffle)
        btnRepeat = view.findViewById(R.id.btnRepeat)
        btnFavorite = view.findViewById(R.id.btnFavorite)
    }
    
    /**
     * إعداد المراقبين للبيانات
     */
    private fun setupObservers() {
        // مراقبة الأغنية الحالية
        viewModel.currentSong.observe(viewLifecycleOwner) { song ->
            updateSongInfo(song)
        }
        
        // مراقبة حالة التشغيل
        viewModel.isPlaying.observe(viewLifecycleOwner) { isPlaying ->
            updatePlayPauseButton(isPlaying)
        }
        
        // مراقبة موضع التشغيل
        viewModel.currentPosition.observe(viewLifecycleOwner) { position ->
            if (!isUserSeeking) {
                updateSeekBar(position)
            }
        }
        
        // مراقبة مدة الأغنية
        viewModel.duration.observe(viewLifecycleOwner) { duration ->
            updateDuration(duration)
        }
        
        // مراقبة حالة التكرار
        viewModel.repeatMode.observe(viewLifecycleOwner) { repeatMode ->
            updateRepeatButton(repeatMode)
        }
        
        // مراقبة حالة التشغيل العشوائي
        viewModel.isShuffleEnabled.observe(viewLifecycleOwner) { isShuffleEnabled ->
            updateShuffleButton(isShuffleEnabled)
        }
    }
    
    /**
     * إعداد المستمعين للأزرار
     */
    private fun setupListeners() {
        btnPlayPause.setOnClickListener {
            viewModel.togglePlayPause()
        }
        
        btnNext.setOnClickListener {
            viewModel.playNext()
        }
        
        btnPrevious.setOnClickListener {
            viewModel.playPrevious()
        }
        
        btnShuffle.setOnClickListener {
            viewModel.toggleShuffle()
        }
        
        btnRepeat.setOnClickListener {
            viewModel.toggleRepeat()
        }
        
        btnFavorite.setOnClickListener {
            viewModel.toggleCurrentSongFavorite()
        }
        
        // SeekBar listener
        seekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    updateCurrentTimeText(progress)
                }
            }
            
            override fun onStartTrackingTouch(seekBar: SeekBar?) {
                isUserSeeking = true
            }
            
            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                isUserSeeking = false
                seekBar?.progress?.let { progress ->
                    viewModel.seekTo(progress)
                }
            }
        })
    }
    
    /**
     * تحديث معلومات الأغنية
     */
    private fun updateSongInfo(song: Song?) {
        if (song != null) {
            songTitle.text = song.title
            artistName.text = song.getArtistOrDefault()
            albumName.text = song.getAlbumOrDefault()
            
            // تحميل صورة الغلاف
            ImageLoader.loadSongImage(
                context = requireContext(),
                song = song,
                imageView = albumCover,
                placeholder = R.drawable.default_album_cover
            )

            // تحديث الخلفية الضبابية
            ImageLoader.loadBlurredSongImage(
                context = requireContext(),
                song = song,
                imageView = backgroundImage,
                blurRadius = 25,
                placeholder = R.drawable.default_album_cover
            )
            
            // تحديث زر المفضلة
            updateFavoriteButton(song.isFavorite)
        } else {
            // إخفاء المعلومات إذا لم تكن هناك أغنية
            songTitle.text = getString(R.string.no_song_playing)
            artistName.text = ""
            albumName.text = ""
            albumCover.setImageResource(R.drawable.default_album_cover)
        }
    }
    
    /**
     * تحديث زر التشغيل/الإيقاف
     */
    private fun updatePlayPauseButton(isPlaying: Boolean) {
        btnPlayPause.setImageResource(
            if (isPlaying) R.drawable.ic_pause else R.drawable.ic_play
        )
    }
    
    /**
     * تحديث شريط التقدم
     */
    private fun updateSeekBar(position: Int) {
        seekBar.progress = position
        updateCurrentTimeText(position)
    }
    
    /**
     * تحديث المدة الإجمالية
     */
    private fun updateDuration(duration: Int) {
        seekBar.max = duration
        totalTime.text = formatTime(duration)
    }
    
    /**
     * تحديث نص الوقت الحالي
     */
    private fun updateCurrentTimeText(position: Int) {
        currentTime.text = formatTime(position)
    }
    
    /**
     * تحديث زر التكرار
     */
    private fun updateRepeatButton(repeatMode: com.musicplayer.pro.models.RepeatMode) {
        val iconRes = when (repeatMode) {
            com.musicplayer.pro.models.RepeatMode.OFF -> R.drawable.ic_repeat_off
            com.musicplayer.pro.models.RepeatMode.ONE -> R.drawable.ic_repeat_one
            com.musicplayer.pro.models.RepeatMode.ALL -> R.drawable.ic_repeat_all
        }
        btnRepeat.setImageResource(iconRes)
    }
    
    /**
     * تحديث زر التشغيل العشوائي
     */
    private fun updateShuffleButton(isShuffleEnabled: Boolean) {
        btnShuffle.setImageResource(
            if (isShuffleEnabled) R.drawable.ic_shuffle_on else R.drawable.ic_shuffle_off
        )
    }
    
    /**
     * تحديث زر المفضلة
     */
    private fun updateFavoriteButton(isFavorite: Boolean) {
        btnFavorite.setImageResource(
            if (isFavorite) R.drawable.ic_favorite_filled else R.drawable.ic_favorite_outline
        )
    }
    
    /**
     * تنسيق الوقت
     */
    private fun formatTime(timeMs: Int): String {
        val minutes = timeMs / 1000 / 60
        val seconds = (timeMs / 1000) % 60
        return String.format("%d:%02d", minutes, seconds)
    }
}
