package com.musicplayer.pro.models

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * نموذج بيانات قائمة التشغيل
 */
@Parcelize
data class Playlist(
    val id: Long = 0L,
    val name: String = "",
    val description: String = "",
    val songIds: List<Long> = emptyList(),
    val coverImagePath: String = "",
    val dateCreated: Long = System.currentTimeMillis(),
    val dateModified: Long = System.currentTimeMillis(),
    val isSystemPlaylist: Boolean = false,
    val playCount: Int = 0,
    val totalDuration: Long = 0L,
    val isFavorite: Boolean = false
) : Parcelable {
    
    /**
     * الحصول على عدد الأغاني
     */
    fun getSongCount(): Int = songIds.size
    
    /**
     * الحصول على المدة الإجمالية بصيغة منسقة
     */
    fun getFormattedDuration(): String {
        val hours = (totalDuration / 1000) / 3600
        val minutes = ((totalDuration / 1000) % 3600) / 60
        val seconds = (totalDuration / 1000) % 60
        
        return when {
            hours > 0 -> String.format("%02d:%02d:%02d", hours, minutes, seconds)
            else -> String.format("%02d:%02d", minutes, seconds)
        }
    }
    
    /**
     * التحقق من وجود أغنية في القائمة
     */
    fun containsSong(songId: Long): Boolean = songIds.contains(songId)
    
    /**
     * إضافة أغنية للقائمة
     */
    fun addSong(songId: Long): Playlist {
        return if (!containsSong(songId)) {
            copy(
                songIds = songIds + songId,
                dateModified = System.currentTimeMillis()
            )
        } else {
            this
        }
    }
    
    /**
     * إزالة أغنية من القائمة
     */
    fun removeSong(songId: Long): Playlist {
        return copy(
            songIds = songIds - songId,
            dateModified = System.currentTimeMillis()
        )
    }
    
    /**
     * إعادة ترتيب الأغاني
     */
    fun reorderSongs(newOrder: List<Long>): Playlist {
        return copy(
            songIds = newOrder,
            dateModified = System.currentTimeMillis()
        )
    }
    
    companion object {
        /**
         * قائمة المفضلة
         */
        fun favorites(): Playlist {
            return Playlist(
                id = -1L,
                name = "المفضلة",
                description = "الأغاني المفضلة لديك",
                isSystemPlaylist = true
            )
        }
        
        /**
         * قائمة الأكثر تشغيلاً
         */
        fun mostPlayed(): Playlist {
            return Playlist(
                id = -2L,
                name = "الأكثر تشغيلاً",
                description = "الأغاني التي تستمع إليها كثيراً",
                isSystemPlaylist = true
            )
        }
        
        /**
         * قائمة المضافة حديثاً
         */
        fun recentlyAdded(): Playlist {
            return Playlist(
                id = -3L,
                name = "المضافة حديثاً",
                description = "الأغاني المضافة مؤخراً",
                isSystemPlaylist = true
            )
        }
    }
}

/**
 * نموذج بيانات الألبوم
 */
@Parcelize
data class Album(
    val id: Long = 0L,
    val name: String = "",
    val artist: String = "",
    val artistId: Long = 0L,
    val songCount: Int = 0,
    val year: Int = 0,
    val duration: Long = 0L,
    val albumArtPath: String = "",
    val firstSongPath: String = "",
    val dateAdded: Long = 0L
) : Parcelable {
    
    /**
     * الحصول على اسم الألبوم المنسق
     */
    fun getFormattedName(): String {
        return when {
            name.isBlank() -> "ألبوم غير معروف"
            name.contains("unknown", ignoreCase = true) -> "ألبوم غير معروف"
            else -> name
        }
    }
    
    /**
     * الحصول على اسم الفنان المنسق
     */
    fun getFormattedArtist(): String {
        return when {
            artist.isBlank() -> "فنان غير معروف"
            artist.contains("unknown", ignoreCase = true) -> "فنان غير معروف"
            else -> artist
        }
    }
    
    /**
     * الحصول على معلومات الألبوم
     */
    fun getAlbumInfo(): String {
        return buildString {
            append("$songCount أغنية")
            if (year > 0) append(" • $year")
            if (duration > 0) {
                val minutes = (duration / 1000) / 60
                append(" • ${minutes} دقيقة")
            }
        }
    }
}

/**
 * نموذج بيانات الفنان
 */
@Parcelize
data class Artist(
    val id: Long = 0L,
    val name: String = "",
    val albumCount: Int = 0,
    val songCount: Int = 0,
    val duration: Long = 0L,
    val albumArtPath: String = ""
) : Parcelable {
    
    /**
     * الحصول على اسم الفنان المنسق
     */
    fun getFormattedName(): String {
        return when {
            name.isBlank() -> "فنان غير معروف"
            name.contains("unknown", ignoreCase = true) -> "فنان غير معروف"
            else -> name
        }
    }
    
    /**
     * الحصول على معلومات الفنان
     */
    fun getArtistInfo(): String {
        return buildString {
            append("$albumCount ألبوم")
            append(" • $songCount أغنية")
            if (duration > 0) {
                val hours = (duration / 1000) / 3600
                if (hours > 0) append(" • ${hours} ساعة")
            }
        }
    }
}

/**
 * نموذج بيانات النوع الموسيقي
 */
@Parcelize
data class Genre(
    val id: Long = 0L,
    val name: String = "",
    val songCount: Int = 0
) : Parcelable {
    
    /**
     * الحصول على اسم النوع المنسق
     */
    fun getFormattedName(): String {
        return when {
            name.isBlank() -> "نوع غير معروف"
            name.contains("unknown", ignoreCase = true) -> "نوع غير معروف"
            else -> name
        }
    }
}

/**
 * نموذج بيانات التحميل
 */
@Parcelize
data class Download(
    val id: Long = 0L,
    val title: String = "",
    val artist: String = "",
    val url: String = "",
    val fileName: String = "",
    val filePath: String = "",
    val fileSize: Long = 0L,
    val downloadedSize: Long = 0L,
    val status: DownloadStatus = DownloadStatus.PENDING,
    val quality: String = "",
    val format: String = "",
    val dateCreated: Long = System.currentTimeMillis(),
    val dateCompleted: Long = 0L,
    val error: String = ""
) : Parcelable {
    
    /**
     * الحصول على نسبة التحميل
     */
    fun getProgress(): Int {
        return if (fileSize > 0) {
            ((downloadedSize * 100) / fileSize).toInt()
        } else {
            0
        }
    }
    
    /**
     * الحصول على حجم الملف المحمل بصيغة منسقة
     */
    fun getFormattedDownloadedSize(): String {
        return formatFileSize(downloadedSize)
    }
    
    /**
     * الحصول على حجم الملف الكامل بصيغة منسقة
     */
    fun getFormattedFileSize(): String {
        return formatFileSize(fileSize)
    }
    
    /**
     * تنسيق حجم الملف
     */
    private fun formatFileSize(size: Long): String {
        return when {
            size < 1024 -> "${size} B"
            size < 1024 * 1024 -> "${size / 1024} KB"
            size < 1024 * 1024 * 1024 -> "${size / (1024 * 1024)} MB"
            else -> "${size / (1024 * 1024 * 1024)} GB"
        }
    }
    
    /**
     * التحقق من اكتمال التحميل
     */
    fun isCompleted(): Boolean = status == DownloadStatus.COMPLETED
    
    /**
     * التحقق من فشل التحميل
     */
    fun isFailed(): Boolean = status == DownloadStatus.FAILED
    
    /**
     * التحقق من جاري التحميل
     */
    fun isDownloading(): Boolean = status == DownloadStatus.DOWNLOADING
}

/**
 * حالات التحميل
 */
enum class DownloadStatus {
    PENDING,      // في الانتظار
    DOWNLOADING,  // جاري التحميل
    PAUSED,       // متوقف مؤقتاً
    COMPLETED,    // مكتمل
    FAILED,       // فشل
    CANCELLED     // ملغي
}

/**
 * نموذج بيانات الثيم
 */
@Parcelize
data class Theme(
    val id: Int = 0,
    val name: String = "",
    val primaryColor: Int = 0,
    val secondaryColor: Int = 0,
    val backgroundColor: Int = 0,
    val surfaceColor: Int = 0,
    val isDark: Boolean = false
) : Parcelable

/**
 * نموذج بيانات الإعدادات
 */
@Parcelize
data class AppSettings(
    val selectedThemeId: Int = 0,
    val isShuffleEnabled: Boolean = false,
    val repeatMode: RepeatMode = RepeatMode.OFF,
    val audioQuality: AudioQuality = AudioQuality.HIGH,
    val isEqualizerEnabled: Boolean = false,
    val equalizerPreset: String = "",
    val sleepTimerMinutes: Int = 0,
    val isAutoScanEnabled: Boolean = true,
    val scanFolders: List<String> = emptyList(),
    val excludeFolders: List<String> = emptyList(),
    val language: String = "ar",
    val isNotificationsEnabled: Boolean = true,
    val isVibrateEnabled: Boolean = true,
    val isKeepScreenOnEnabled: Boolean = false,
    val crossfadeDuration: Int = 0,
    val gaplessPlayback: Boolean = true,
    val replayGainMode: ReplayGainMode = ReplayGainMode.OFF,
    val bassBoost: Int = 0,
    val virtualizer: Int = 0,
    val loudnessEnhancer: Int = 0
) : Parcelable

/**
 * أنماط التكرار
 */
enum class RepeatMode {
    OFF,      // بدون تكرار
    ONE,      // تكرار أغنية واحدة
    ALL       // تكرار الكل
}

/**
 * جودة الصوت
 */
enum class AudioQuality {
    LOW,      // منخفضة
    MEDIUM,   // متوسطة
    HIGH,     // عالية
    LOSSLESS  // بدون فقدان
}

/**
 * أنماط Replay Gain
 */
enum class ReplayGainMode {
    OFF,      // مغلق
    TRACK,    // حسب المقطوعة
    ALBUM     // حسب الألبوم
}
