#!/usr/bin/env python3
"""
سكريبت إعداد وتشغيل مشروع Music Player Pro - Kotlin
يقوم بتحميل جميع المتطلبات وإعداد البيئة وبناء المشروع
"""

import os
import sys
import subprocess
import platform
import urllib.request
import zipfile
import shutil
from pathlib import Path

class MusicPlayerSetup:
    def __init__(self):
        self.system = platform.system().lower()
        self.project_dir = Path(__file__).parent
        self.tools_dir = self.project_dir / "tools"
        self.sdk_dir = self.project_dir / "android-sdk"
        
        # URLs للتحميل
        self.urls = {
            "android_studio": {
                "windows": "https://redirector.gvt1.com/edgedl/android/studio/install/2023.1.1.28/android-studio-2023.1.1.28-windows.exe",
                "linux": "https://redirector.gvt1.com/edgedl/android/studio/ide-zips/2023.1.1.28/android-studio-2023.1.1.28-linux.tar.gz",
                "darwin": "https://redirector.gvt1.com/edgedl/android/studio/install/2023.1.1.28/android-studio-2023.1.1.28-mac.dmg"
            },
            "android_sdk": "https://dl.google.com/android/repository/commandlinetools-{}-9477386_latest.zip",
            "gradle": "https://services.gradle.org/distributions/gradle-8.4-bin.zip",
            "kotlin": "https://github.com/JetBrains/kotlin/releases/download/v1.9.21/kotlin-compiler-1.9.21.zip"
        }
        
    def print_header(self, title):
        """طباعة عنوان مع تنسيق"""
        print("\n" + "="*60)
        print(f"🚀 {title}")
        print("="*60)
        
    def run_command(self, command, cwd=None, shell=True):
        """تشغيل أمر مع معالجة الأخطاء"""
        try:
            print(f"📝 تشغيل: {command}")
            result = subprocess.run(
                command, 
                shell=shell, 
                cwd=cwd, 
                capture_output=True, 
                text=True,
                timeout=300
            )
            
            if result.returncode == 0:
                print("✅ نجح الأمر")
                return True, result.stdout
            else:
                print(f"❌ فشل الأمر: {result.stderr}")
                return False, result.stderr
                
        except subprocess.TimeoutExpired:
            print("⏰ انتهت مهلة الأمر")
            return False, "Timeout"
        except Exception as e:
            print(f"❌ خطأ: {e}")
            return False, str(e)
    
    def download_file(self, url, destination):
        """تحميل ملف من الإنترنت"""
        try:
            print(f"📥 تحميل من: {url}")
            print(f"📁 إلى: {destination}")
            
            # إنشاء المجلد إذا لم يكن موجوداً
            destination.parent.mkdir(parents=True, exist_ok=True)
            
            # تحميل الملف
            urllib.request.urlretrieve(url, destination)
            print("✅ تم التحميل بنجاح")
            return True
            
        except Exception as e:
            print(f"❌ فشل التحميل: {e}")
            return False
    
    def extract_zip(self, zip_path, extract_to):
        """استخراج ملف مضغوط"""
        try:
            print(f"📦 استخراج: {zip_path}")
            print(f"📁 إلى: {extract_to}")
            
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(extract_to)
            
            print("✅ تم الاستخراج بنجاح")
            return True
            
        except Exception as e:
            print(f"❌ فشل الاستخراج: {e}")
            return False
    
    def check_java(self):
        """فحص وجود Java"""
        self.print_header("فحص Java")
        
        success, output = self.run_command("java -version")
        if success:
            print("✅ Java موجود")
            print(output)
            return True
        else:
            print("❌ Java غير موجود")
            return self.install_java()
    
    def install_java(self):
        """تثبيت Java"""
        print("📥 تثبيت Java...")
        
        if self.system == "windows":
            # تحميل وتثبيت OpenJDK
            java_url = "https://download.java.net/java/GA/jdk11/9/GPL/openjdk-11.0.2_windows-x64_bin.zip"
            java_zip = self.tools_dir / "openjdk.zip"
            
            if self.download_file(java_url, java_zip):
                java_dir = self.tools_dir / "java"
                if self.extract_zip(java_zip, java_dir):
                    # إضافة Java إلى PATH
                    java_bin = java_dir / "jdk-11.0.2" / "bin"
                    os.environ["JAVA_HOME"] = str(java_dir / "jdk-11.0.2")
                    os.environ["PATH"] = str(java_bin) + os.pathsep + os.environ["PATH"]
                    return True
        
        elif self.system == "linux":
            # تثبيت Java على Linux
            commands = [
                "sudo apt update",
                "sudo apt install -y openjdk-11-jdk"
            ]
            
            for cmd in commands:
                success, _ = self.run_command(cmd)
                if not success:
                    return False
            return True
        
        return False
    
    def setup_android_sdk(self):
        """إعداد Android SDK"""
        self.print_header("إعداد Android SDK")
        
        # تحديد نظام التشغيل
        if self.system == "windows":
            platform_name = "win"
        elif self.system == "linux":
            platform_name = "linux"
        else:
            platform_name = "mac"
        
        # تحميل Command Line Tools
        sdk_url = self.urls["android_sdk"].format(platform_name)
        sdk_zip = self.tools_dir / "android-sdk-tools.zip"
        
        if not self.download_file(sdk_url, sdk_zip):
            return False
        
        # استخراج SDK
        cmdline_tools_dir = self.sdk_dir / "cmdline-tools"
        if not self.extract_zip(sdk_zip, cmdline_tools_dir):
            return False
        
        # إعادة تنظيم المجلدات
        temp_dir = cmdline_tools_dir / "cmdline-tools"
        latest_dir = cmdline_tools_dir / "latest"
        
        if temp_dir.exists():
            shutil.move(str(temp_dir), str(latest_dir))
        
        # إعداد متغيرات البيئة
        os.environ["ANDROID_HOME"] = str(self.sdk_dir)
        os.environ["ANDROID_SDK_ROOT"] = str(self.sdk_dir)
        
        # إضافة أدوات SDK إلى PATH
        sdk_tools = [
            self.sdk_dir / "cmdline-tools" / "latest" / "bin",
            self.sdk_dir / "platform-tools",
            self.sdk_dir / "tools"
        ]
        
        for tool_path in sdk_tools:
            if tool_path.exists():
                os.environ["PATH"] = str(tool_path) + os.pathsep + os.environ["PATH"]
        
        # تثبيت مكونات SDK المطلوبة
        sdkmanager = latest_dir / "bin" / ("sdkmanager.bat" if self.system == "windows" else "sdkmanager")
        
        if sdkmanager.exists():
            # قبول التراخيص
            self.run_command(f'echo y | "{sdkmanager}" --licenses')
            
            # تثبيت المكونات
            components = [
                "platform-tools",
                "platforms;android-34",
                "platforms;android-33",
                "build-tools;34.0.0",
                "build-tools;33.0.0",
                "ndk;25.2.9519653",
                "cmake;3.22.1"
            ]
            
            for component in components:
                self.run_command(f'"{sdkmanager}" "{component}"')
        
        print("✅ تم إعداد Android SDK")
        return True
    
    def setup_gradle(self):
        """إعداد Gradle"""
        self.print_header("إعداد Gradle")
        
        gradle_zip = self.tools_dir / "gradle.zip"
        gradle_dir = self.tools_dir / "gradle"
        
        if not self.download_file(self.urls["gradle"], gradle_zip):
            return False
        
        if not self.extract_zip(gradle_zip, gradle_dir):
            return False
        
        # إضافة Gradle إلى PATH
        gradle_bin = gradle_dir / "gradle-8.4" / "bin"
        os.environ["PATH"] = str(gradle_bin) + os.pathsep + os.environ["PATH"]
        
        print("✅ تم إعداد Gradle")
        return True
    
    def create_missing_files(self):
        """إنشاء الملفات المفقودة"""
        self.print_header("إنشاء الملفات المفقودة")
        
        # إنشاء gradle.properties
        gradle_props = self.project_dir / "gradle.properties"
        if not gradle_props.exists():
            gradle_content = """
# Gradle Properties
org.gradle.jvmargs=-Xmx4g -Dfile.encoding=UTF-8
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.configureondemand=true

# Android Properties
android.useAndroidX=true
android.enableJetifier=true
android.nonTransitiveRClass=true
android.defaults.buildfeatures.buildconfig=true
android.defaults.buildfeatures.aidl=true
android.defaults.buildfeatures.renderscript=true

# Kotlin Properties
kotlin.code.style=official
kotlin.incremental=true
kotlin.incremental.android=true
kotlin.incremental.js=true
kotlin.build.report.output=file
"""
            gradle_props.write_text(gradle_content.strip())
        
        # إنشاء settings.gradle
        settings_gradle = self.project_dir / "settings.gradle"
        if not settings_gradle.exists():
            settings_content = """
pluginManagement {
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}

dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        maven { url 'https://jitpack.io' }
    }
}

rootProject.name = "MusicPlayerPro"
include ':app'
"""
            settings_gradle.write_text(settings_content.strip())
        
        # إنشاء build.gradle الجذر
        root_build_gradle = self.project_dir / "build.gradle"
        if not root_build_gradle.exists():
            root_build_content = """
buildscript {
    ext.kotlin_version = "1.9.21"
    dependencies {
        classpath 'com.android.tools.build:gradle:8.2.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

plugins {
    id 'com.android.application' version '8.2.0' apply false
    id 'org.jetbrains.kotlin.android' version '1.9.21' apply false
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
"""
            root_build_gradle.write_text(root_build_content.strip())
        
        # إنشاء مجلدات المشروع
        app_dir = self.project_dir / "app"
        app_dir.mkdir(exist_ok=True)
        
        src_main_dir = app_dir / "src" / "main"
        src_main_dir.mkdir(parents=True, exist_ok=True)
        
        # إنشاء مجلدات الموارد
        res_dirs = [
            "layout", "values", "values-ar", "drawable", 
            "mipmap-hdpi", "mipmap-mdpi", "mipmap-xhdpi", 
            "mipmap-xxhdpi", "mipmap-xxxhdpi"
        ]
        
        for res_dir in res_dirs:
            (src_main_dir / "res" / res_dir).mkdir(parents=True, exist_ok=True)
        
        print("✅ تم إنشاء الملفات المفقودة")
        return True
    
    def build_project(self):
        """بناء المشروع"""
        self.print_header("بناء المشروع")
        
        # التأكد من وجود gradlew
        gradlew = self.project_dir / ("gradlew.bat" if self.system == "windows" else "gradlew")
        
        if not gradlew.exists():
            print("📝 إنشاء Gradle Wrapper...")
            success, _ = self.run_command("gradle wrapper", cwd=self.project_dir)
            if not success:
                print("❌ فشل في إنشاء Gradle Wrapper")
                return False
        
        # جعل gradlew قابل للتنفيذ على Linux/Mac
        if self.system != "windows":
            os.chmod(gradlew, 0o755)
        
        # تنظيف المشروع
        print("🧹 تنظيف المشروع...")
        clean_cmd = str(gradlew) + " clean"
        success, _ = self.run_command(clean_cmd, cwd=self.project_dir)
        
        # بناء المشروع
        print("🏗️ بناء المشروع...")
        build_cmd = str(gradlew) + " assembleDebug"
        success, output = self.run_command(build_cmd, cwd=self.project_dir)
        
        if success:
            print("✅ تم بناء المشروع بنجاح!")
            
            # البحث عن APK
            apk_path = self.project_dir / "app" / "build" / "outputs" / "apk" / "debug"
            if apk_path.exists():
                apk_files = list(apk_path.glob("*.apk"))
                if apk_files:
                    print(f"📱 تم إنشاء APK: {apk_files[0]}")
                    return True
        
        print("❌ فشل في بناء المشروع")
        return False
    
    def run_setup(self):
        """تشغيل الإعداد الكامل"""
        print("🎵 Music Player Pro - Kotlin Setup")
        print("=" * 50)
        
        steps = [
            ("فحص Java", self.check_java),
            ("إعداد Android SDK", self.setup_android_sdk),
            ("إعداد Gradle", self.setup_gradle),
            ("إنشاء الملفات المفقودة", self.create_missing_files),
            ("بناء المشروع", self.build_project)
        ]
        
        for step_name, step_func in steps:
            try:
                if not step_func():
                    print(f"❌ فشل في: {step_name}")
                    return False
            except Exception as e:
                print(f"❌ خطأ في {step_name}: {e}")
                return False
        
        print("\n🎉 تم إعداد المشروع بنجاح!")
        print("📱 يمكنك الآن فتح المشروع في Android Studio")
        print("🚀 أو تشغيل: ./gradlew assembleDebug لبناء APK")
        
        return True

def main():
    """الدالة الرئيسية"""
    setup = MusicPlayerSetup()
    
    try:
        success = setup.run_setup()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الإعداد بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
