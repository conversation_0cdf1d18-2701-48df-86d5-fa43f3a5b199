#Sun Jul 13 14:58:23 AST 2025
com.musicplayer.pro.app-main-5\:/drawable/play_button_background.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\play_button_background.xml
com.musicplayer.pro.app-packageDebugResources-2\:/layout/item_song.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\layout\\item_song.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_info.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_info.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_delete.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_delete.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_exit.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_exit.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_theme.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_theme.xml
com.musicplayer.pro.app-main-5\:/drawable/playing_overlay.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\playing_overlay.xml
com.musicplayer.pro.app-packageDebugResources-2\:/layout/bottom_music_bar.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\layout\\bottom_music_bar.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_favorite_outline.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_favorite_outline.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_high_quality.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_high_quality.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_warning.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_warning.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_surround_sound.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_surround_sound.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_playlist_add.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_playlist_add.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_language.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_language.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_add.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_add.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_storage.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_storage.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_file_format.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_file_format.xml
com.musicplayer.pro.app-main-5\:/drawable/default_album_cover.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\default_album_cover.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_reset.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_reset.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_update.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_update.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_backup.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_backup.xml
com.musicplayer.pro.app-main-5\:/drawable/bottom_bar_background.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\bottom_bar_background.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_music_library.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_music_library.xml
com.musicplayer.pro.app-packageDebugResources-2\:/layout/activity_equalizer.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_equalizer.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_bass.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_bass.xml
com.musicplayer.pro.app-main-5\:/xml/backup_rules.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\xml\\backup_rules.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_image.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_image.xml
com.musicplayer.pro.app-packageDebugResources-2\:/layout/item_equalizer_band.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\layout\\item_equalizer_band.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_favorite_filled.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_favorite_filled.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_palette.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_palette.xml
com.musicplayer.pro.app-main-5\:/mipmap-hdpi/ic_launcher.png=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-hdpi-v4\\ic_launcher.png
com.musicplayer.pro.app-main-5\:/drawable/ic_download_quality.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_download_quality.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_settings.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_settings.xml
com.musicplayer.pro.app-packageDebugResources-2\:/layout/fragment_main.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\layout\\fragment_main.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_license.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_license.xml
com.musicplayer.pro.app-main-5\:/menu/main_menu.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\menu\\main_menu.xml
com.musicplayer.pro.app-main-5\:/xml/automotive_app_desc.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\xml\\automotive_app_desc.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_shuffle_on.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_shuffle_on.xml
com.musicplayer.pro.app-packageDebugResources-2\:/layout/activity_main.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_main.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_folder.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_folder.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_clear.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_clear.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_repeat_one.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_repeat_one.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_pause.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_pause.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_refresh.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_refresh.xml
com.musicplayer.pro.app-main-5\:/xml/data_extraction_rules.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\xml\\data_extraction_rules.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_equalizer.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_equalizer.xml
com.musicplayer.pro.app-main-5\:/xml/file_paths.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\xml\\file_paths.xml
com.musicplayer.pro.app-main-5\:/drawable/quality_badge.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\quality_badge.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_audio.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_audio.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_link.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_link.xml
com.musicplayer.pro.app-packageDebugResources-2\:/layout/item_download.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\layout\\item_download.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_download.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_download.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_share.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_share.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_loading.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_loading.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_repeat_off.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_repeat_off.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_music_note.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_music_note.xml
com.musicplayer.pro.app-main-5\:/drawable/spinner_background.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\spinner_background.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_audio_effects.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_audio_effects.xml
com.musicplayer.pro.app-main-5\:/xml/preferences.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\xml\\preferences.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_shuffle_off.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_shuffle_off.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_repeat_all.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_repeat_all.xml
com.musicplayer.pro.app-packageDebugResources-2\:/layout/dialog_add_download.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\layout\\dialog_add_download.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_restore.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_restore.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_wifi.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_wifi.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_play.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_play.xml
com.musicplayer.pro.app-main-5\:/drawable/control_button_background.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\control_button_background.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_skip_previous.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_skip_previous.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_skip_next.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_skip_next.xml
com.musicplayer.pro.app-main-5\:/mipmap-hdpi/ic_launcher_round.png=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-hdpi-v4\\ic_launcher_round.png
com.musicplayer.pro.app-packageDebugResources-2\:/layout/fragment_now_playing.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\layout\\fragment_now_playing.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_search.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_search.xml
com.musicplayer.pro.app-packageDebugResources-2\:/layout/activity_settings.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_settings.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_now_playing.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_now_playing.xml
com.musicplayer.pro.app-packageDebugResources-2\:/layout/fragment_download.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\layout\\fragment_download.xml
com.musicplayer.pro.app-main-5\:/drawable/ic_star.xml=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_star.xml
