package com.musicplayer.pro.fragments

import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.graphics.Bitmap
import android.os.Bundle
import android.view.*
import android.view.animation.LinearInterpolator
import android.widget.*
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.slider.Slider
import com.musicplayer.pro.R
import com.musicplayer.pro.models.Song
import com.musicplayer.pro.models.RepeatMode
import com.musicplayer.pro.viewmodels.NowPlayingViewModel
import com.musicplayer.pro.utils.ImageUtils
import com.musicplayer.pro.utils.ColorExtractor
import com.musicplayer.pro.views.CircularProgressView
import com.musicplayer.pro.views.VisualizerView

/**
 * شاشة التشغيل الحالي
 * تعرض تفاصيل الأغنية الحالية وعناصر التحكم
 */
class NowPlayingFragment : Fragment() {
    
    companion object {
        private const val TAG = "NowPlayingFragment"
    }
    
    // ViewModel
    private lateinit var viewModel: NowPlayingViewModel
    
    // UI Components - Song Info
    private lateinit var albumArtImageView: ImageView
    private lateinit var songTitleTextView: TextView
    private lateinit var artistNameTextView: TextView
    private lateinit var albumNameTextView: TextView
    
    // UI Components - Progress
    private lateinit var progressSlider: Slider
    private lateinit var currentTimeTextView: TextView
    private lateinit var totalTimeTextView: TextView
    private lateinit var circularProgress: CircularProgressView
    
    // UI Components - Controls
    private lateinit var playPauseButton: ImageButton
    private lateinit var nextButton: ImageButton
    private lateinit var previousButton: ImageButton
    private lateinit var shuffleButton: ImageButton
    private lateinit var repeatButton: ImageButton
    
    // UI Components - Additional
    private lateinit var favoriteButton: ImageButton
    private lateinit var menuButton: ImageButton
    private lateinit var visualizerView: VisualizerView
    private lateinit var backgroundImageView: ImageView
    
    // Animation
    private var rotationAnimator: ObjectAnimator? = null
    private var isUserSeeking = false
    
    // Background
    private var currentBackgroundColor = 0
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_now_playing, container, false)
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        // تهيئة ViewModel
        viewModel = ViewModelProvider(this)[NowPlayingViewModel::class.java]
        
        // تهيئة UI
        initializeViews(view)
        setupControls()
        setupProgressSlider()
        
        // مراقبة البيانات
        observeData()
        
        // تحميل البيانات الحالية
        viewModel.loadCurrentSong()
    }
    
    /**
     * تهيئة العناصر
     */
    private fun initializeViews(view: View) {
        // Song Info
        albumArtImageView = view.findViewById(R.id.albumArtImageView)
        songTitleTextView = view.findViewById(R.id.songTitleTextView)
        artistNameTextView = view.findViewById(R.id.artistNameTextView)
        albumNameTextView = view.findViewById(R.id.albumNameTextView)
        
        // Progress
        progressSlider = view.findViewById(R.id.progressSlider)
        currentTimeTextView = view.findViewById(R.id.currentTimeTextView)
        totalTimeTextView = view.findViewById(R.id.totalTimeTextView)
        circularProgress = view.findViewById(R.id.circularProgress)
        
        // Controls
        playPauseButton = view.findViewById(R.id.playPauseButton)
        nextButton = view.findViewById(R.id.nextButton)
        previousButton = view.findViewById(R.id.previousButton)
        shuffleButton = view.findViewById(R.id.shuffleButton)
        repeatButton = view.findViewById(R.id.repeatButton)
        
        // Additional
        favoriteButton = view.findViewById(R.id.favoriteButton)
        menuButton = view.findViewById(R.id.menuButton)
        visualizerView = view.findViewById(R.id.visualizerView)
        backgroundImageView = view.findViewById(R.id.backgroundImageView)
    }
    
    /**
     * إعداد عناصر التحكم
     */
    private fun setupControls() {
        // Play/Pause
        playPauseButton.setOnClickListener {
            viewModel.togglePlayPause()
        }
        
        // Next
        nextButton.setOnClickListener {
            viewModel.playNext()
        }
        
        // Previous
        previousButton.setOnClickListener {
            viewModel.playPrevious()
        }
        
        // Shuffle
        shuffleButton.setOnClickListener {
            viewModel.toggleShuffle()
        }
        
        // Repeat
        repeatButton.setOnClickListener {
            viewModel.toggleRepeat()
        }
        
        // Favorite
        favoriteButton.setOnClickListener {
            viewModel.toggleFavorite()
        }
        
        // Menu
        menuButton.setOnClickListener {
            showSongMenu()
        }
        
        // Album Art Click (للدوران)
        albumArtImageView.setOnClickListener {
            if (rotationAnimator?.isRunning == true) {
                pauseRotation()
            } else {
                startRotation()
            }
        }
    }
    
    /**
     * إعداد شريط التقدم
     */
    private fun setupProgressSlider() {
        progressSlider.addOnSliderTouchListener(object : Slider.OnSliderTouchListener {
            override fun onStartTrackingTouch(slider: Slider) {
                isUserSeeking = true
            }
            
            override fun onStopTrackingTouch(slider: Slider) {
                isUserSeeking = false
                viewModel.seekTo(slider.value.toInt())
            }
        })
        
        progressSlider.addOnChangeListener { _, value, fromUser ->
            if (fromUser) {
                updateCurrentTime(value.toInt())
            }
        }
    }
    
    /**
     * مراقبة البيانات
     */
    private fun observeData() {
        // مراقبة الأغنية الحالية
        viewModel.currentSong.observe(viewLifecycleOwner) { song ->
            updateSongInfo(song)
        }
        
        // مراقبة حالة التشغيل
        viewModel.isPlaying.observe(viewLifecycleOwner) { isPlaying ->
            updatePlayPauseButton(isPlaying)
            updateRotationAnimation(isPlaying)
            updateVisualizer(isPlaying)
        }
        
        // مراقبة التقدم
        viewModel.progress.observe(viewLifecycleOwner) { progress ->
            if (!isUserSeeking) {
                updateProgress(progress.current, progress.total)
            }
        }
        
        // مراقبة حالة الخلط
        viewModel.isShuffleEnabled.observe(viewLifecycleOwner) { isEnabled ->
            updateShuffleButton(isEnabled)
        }
        
        // مراقبة نمط التكرار
        viewModel.repeatMode.observe(viewLifecycleOwner) { mode ->
            updateRepeatButton(mode)
        }
        
        // مراقبة حالة المفضلة
        viewModel.isFavorite.observe(viewLifecycleOwner) { isFavorite ->
            updateFavoriteButton(isFavorite)
        }
        
        // مراقبة صورة الألبوم
        viewModel.albumArt.observe(viewLifecycleOwner) { bitmap ->
            updateAlbumArt(bitmap)
        }
    }
    
    /**
     * تحديث معلومات الأغنية
     */
    private fun updateSongInfo(song: Song?) {
        if (song != null) {
            songTitleTextView.text = song.getFormattedTitle()
            artistNameTextView.text = song.getFormattedArtist()
            albumNameTextView.text = song.getFormattedAlbum()
            
            // تحديث الوقت الإجمالي
            totalTimeTextView.text = song.getFormattedDuration()
            progressSlider.valueTo = song.duration.toFloat()
            
            // تحميل صورة الألبوم
            viewModel.loadAlbumArt(song)
        } else {
            // مسح المعلومات
            songTitleTextView.text = getString(R.string.no_song_playing)
            artistNameTextView.text = ""
            albumNameTextView.text = ""
            currentTimeTextView.text = "00:00"
            totalTimeTextView.text = "00:00"
            progressSlider.value = 0f
            progressSlider.valueTo = 100f
        }
    }
    
    /**
     * تحديث زر التشغيل/الإيقاف
     */
    private fun updatePlayPauseButton(isPlaying: Boolean) {
        val iconRes = if (isPlaying) R.drawable.ic_pause else R.drawable.ic_play
        playPauseButton.setImageResource(iconRes)
        
        // تحديث التقدم الدائري
        circularProgress.setPlaying(isPlaying)
    }
    
    /**
     * تحديث التقدم
     */
    private fun updateProgress(current: Int, total: Int) {
        if (total > 0) {
            progressSlider.value = current.toFloat()
            circularProgress.setProgress(current, total)
        }
        updateCurrentTime(current)
    }
    
    /**
     * تحديث الوقت الحالي
     */
    private fun updateCurrentTime(currentMs: Int) {
        val minutes = (currentMs / 1000) / 60
        val seconds = (currentMs / 1000) % 60
        currentTimeTextView.text = String.format("%02d:%02d", minutes, seconds)
    }
    
    /**
     * تحديث زر الخلط
     */
    private fun updateShuffleButton(isEnabled: Boolean) {
        val alpha = if (isEnabled) 1.0f else 0.5f
        shuffleButton.alpha = alpha
        shuffleButton.isSelected = isEnabled
    }
    
    /**
     * تحديث زر التكرار
     */
    private fun updateRepeatButton(mode: RepeatMode) {
        when (mode) {
            RepeatMode.OFF -> {
                repeatButton.setImageResource(R.drawable.ic_repeat)
                repeatButton.alpha = 0.5f
                repeatButton.isSelected = false
            }
            RepeatMode.ALL -> {
                repeatButton.setImageResource(R.drawable.ic_repeat)
                repeatButton.alpha = 1.0f
                repeatButton.isSelected = true
            }
            RepeatMode.ONE -> {
                repeatButton.setImageResource(R.drawable.ic_repeat_one)
                repeatButton.alpha = 1.0f
                repeatButton.isSelected = true
            }
        }
    }
    
    /**
     * تحديث زر المفضلة
     */
    private fun updateFavoriteButton(isFavorite: Boolean) {
        val iconRes = if (isFavorite) R.drawable.ic_favorite_filled else R.drawable.ic_favorite_border
        favoriteButton.setImageResource(iconRes)
        favoriteButton.isSelected = isFavorite
    }
    
    /**
     * تحديث صورة الألبوم
     */
    private fun updateAlbumArt(bitmap: Bitmap?) {
        if (bitmap != null) {
            albumArtImageView.setImageBitmap(bitmap)
            
            // استخراج اللون المهيمن للخلفية
            ColorExtractor.extractDominantColor(bitmap) { color ->
                updateBackgroundColor(color)
            }
        } else {
            albumArtImageView.setImageResource(R.drawable.default_album_art)
            updateBackgroundColor(resources.getColor(R.color.default_background, null))
        }
    }
    
    /**
     * تحديث لون الخلفية
     */
    private fun updateBackgroundColor(color: Int) {
        if (color != currentBackgroundColor) {
            currentBackgroundColor = color
            
            // إنشاء تدرج للخلفية
            val gradientBitmap = ImageUtils.createGradientBackground(
                requireContext(),
                color,
                backgroundImageView.width,
                backgroundImageView.height
            )
            
            backgroundImageView.setImageBitmap(gradientBitmap)
        }
    }
    
    /**
     * بدء تحريك الدوران
     */
    private fun startRotation() {
        rotationAnimator?.cancel()
        rotationAnimator = ObjectAnimator.ofFloat(albumArtImageView, "rotation", 0f, 360f).apply {
            duration = 10000 // 10 ثوانٍ لدورة كاملة
            repeatCount = ValueAnimator.INFINITE
            interpolator = LinearInterpolator()
            start()
        }
    }
    
    /**
     * إيقاف تحريك الدوران مؤقتاً
     */
    private fun pauseRotation() {
        rotationAnimator?.pause()
    }
    
    /**
     * استئناف تحريك الدوران
     */
    private fun resumeRotation() {
        rotationAnimator?.resume()
    }
    
    /**
     * تحديث تحريك الدوران حسب حالة التشغيل
     */
    private fun updateRotationAnimation(isPlaying: Boolean) {
        if (isPlaying) {
            if (rotationAnimator?.isStarted != true) {
                startRotation()
            } else {
                resumeRotation()
            }
        } else {
            pauseRotation()
        }
    }
    
    /**
     * تحديث المُصور المرئي
     */
    private fun updateVisualizer(isPlaying: Boolean) {
        if (isPlaying) {
            visualizerView.startAnimation()
        } else {
            visualizerView.stopAnimation()
        }
    }
    
    /**
     * عرض قائمة الأغنية
     */
    private fun showSongMenu() {
        val bottomSheet = BottomSheetDialog(requireContext())
        val view = layoutInflater.inflate(R.layout.bottom_sheet_song_menu, null)
        
        // إعداد عناصر القائمة
        view.findViewById<TextView>(R.id.menuShare).setOnClickListener {
            viewModel.shareSong()
            bottomSheet.dismiss()
        }
        
        view.findViewById<TextView>(R.id.menuAddToPlaylist).setOnClickListener {
            viewModel.addToPlaylist()
            bottomSheet.dismiss()
        }
        
        view.findViewById<TextView>(R.id.menuSongInfo).setOnClickListener {
            viewModel.showSongInfo()
            bottomSheet.dismiss()
        }
        
        bottomSheet.setContentView(view)
        bottomSheet.show()
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        rotationAnimator?.cancel()
        visualizerView.stopAnimation()
    }
}
