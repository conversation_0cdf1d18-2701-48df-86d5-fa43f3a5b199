<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_download" modulePackage="com.musicplayer.pro" filePath="app\src\main\res\layout\item_download.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/item_download_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="187" endOffset="35"/></Target><Target id="@+id/thumbnailCard" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="17" startOffset="8" endLine="33" endOffset="43"/></Target><Target id="@+id/downloadThumbnail" view="ImageView"><Expressions/><location startLine="26" startOffset="12" endLine="31" endOffset="61"/></Target><Target id="@+id/downloadInfoLayout" view="LinearLayout"><Expressions/><location startLine="36" startOffset="8" endLine="79" endOffset="22"/></Target><Target id="@+id/downloadTitle" view="TextView"><Expressions/><location startLine="47" startOffset="12" endLine="56" endOffset="50"/></Target><Target id="@+id/downloadArtist" view="TextView"><Expressions/><location startLine="58" startOffset="12" endLine="67" endOffset="43"/></Target><Target id="@+id/downloadStatus" view="TextView"><Expressions/><location startLine="69" startOffset="12" endLine="77" endOffset="43"/></Target><Target id="@+id/actionButton" view="ImageButton"><Expressions/><location startLine="82" startOffset="8" endLine="92" endOffset="55"/></Target><Target id="@+id/deleteButton" view="ImageButton"><Expressions/><location startLine="94" startOffset="8" endLine="103" endOffset="55"/></Target><Target id="@+id/downloadProgress" view="ProgressBar"><Expressions/><location startLine="106" startOffset="8" endLine="117" endOffset="33"/></Target><Target id="@+id/progressText" view="TextView"><Expressions/><location startLine="129" startOffset="12" endLine="135" endOffset="34"/></Target><Target id="@+id/downloadSize" view="TextView"><Expressions/><location startLine="142" startOffset="12" endLine="148" endOffset="46"/></Target><Target id="@+id/downloadSpeed" view="TextView"><Expressions/><location startLine="162" startOffset="12" endLine="168" endOffset="39"/></Target><Target id="@+id/downloadEta" view="TextView"><Expressions/><location startLine="175" startOffset="12" endLine="181" endOffset="37"/></Target></Targets></Layout>