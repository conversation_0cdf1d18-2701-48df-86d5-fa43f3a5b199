{"logs": [{"outputFile": "com.musicplayer.pro.app-mergeDebugResources-55:/values-pt/values-pt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e913642d7c50f47db3e63580f9572497\\transformed\\navigation-ui-2.7.6\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,119", "endOffsets": "162,282"}, "to": {"startLines": "106,107", "startColumns": "4,4", "startOffsets": "8174,8286", "endColumns": "111,119", "endOffsets": "8281,8401"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7c108dd56627f30fe94755d1a2faeaf2\\transformed\\core-1.12.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "46,47,48,49,50,51,52,111", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3438,3535,3637,3736,3836,3943,4053,8722", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "3530,3632,3731,3831,3938,4048,4168,8818"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d0962e3cd95d785309666b77b5473090\\transformed\\appcompat-1.6.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "700,820,926,1033,1122,1223,1342,1427,1507,1598,1691,1786,1880,1980,2073,2168,2263,2354,2445,2530,2637,2748,2850,2958,3066,3176,3338,8636", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "815,921,1028,1117,1218,1337,1422,1502,1593,1686,1781,1875,1975,2068,2163,2258,2349,2440,2525,2632,2743,2845,2953,3061,3171,3333,3433,8717"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\210f3162f1672bd532c02d8af1bd50c1\\transformed\\preference-1.2.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,262,341,492,661,748", "endColumns": "69,86,78,150,168,86,80", "endOffsets": "170,257,336,487,656,743,824"}, "to": {"startLines": "53,105,108,109,112,113,114", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4173,8087,8406,8485,8823,8992,9079", "endColumns": "69,86,78,150,168,86,80", "endOffsets": "4238,8169,8480,8631,8987,9074,9155"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f0c4d03d60aaed2968a93a146657af49\\transformed\\jetified-media3-exoplayer-1.2.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,195,267,333,410,477,578,671", "endColumns": "69,69,71,65,76,66,100,92,69", "endOffsets": "120,190,262,328,405,472,573,666,736"}, "to": {"startLines": "78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6264,6334,6404,6476,6542,6619,6686,6787,6880", "endColumns": "69,69,71,65,76,66,100,92,69", "endOffsets": "6329,6399,6471,6537,6614,6681,6782,6875,6945"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\472040ed58573b5e54239e60b254baef\\transformed\\jetified-media3-ui-1.2.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,471,650,731,814,887,986,1082,1156,1222,1318,1413,1479,1548,1615,1686,1804,1921,2042,2109,2195,2271,2345,2443,2543,2607,2671,2724,2782,2830,2891,2956,3018,3084,3154,3218,3279,3345,3410,3476,3529,3593,3671,3749", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,80,82,72,98,95,73,65,95,94,65,68,66,70,117,116,120,66,85,75,73,97,99,63,63,52,57,47,60,64,61,65,69,63,60,65,64,65,52,63,77,77,58", "endOffsets": "280,466,645,726,809,882,981,1077,1151,1217,1313,1408,1474,1543,1610,1681,1799,1916,2037,2104,2190,2266,2340,2438,2538,2602,2666,2719,2777,2825,2886,2951,3013,3079,3149,3213,3274,3340,3405,3471,3524,3588,3666,3744,3803"}, "to": {"startLines": "2,11,15,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,521,4243,4324,4407,4480,4579,4675,4749,4815,4911,5006,5072,5141,5208,5279,5397,5514,5635,5702,5788,5864,5938,6036,6136,6200,6950,7003,7061,7109,7170,7235,7297,7363,7433,7497,7558,7624,7689,7755,7808,7872,7950,8028", "endLines": "10,14,18,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104", "endColumns": "17,12,12,80,82,72,98,95,73,65,95,94,65,68,66,70,117,116,120,66,85,75,73,97,99,63,63,52,57,47,60,64,61,65,69,63,60,65,64,65,52,63,77,77,58", "endOffsets": "330,516,695,4319,4402,4475,4574,4670,4744,4810,4906,5001,5067,5136,5203,5274,5392,5509,5630,5697,5783,5859,5933,6031,6131,6195,6259,6998,7056,7104,7165,7230,7292,7358,7428,7492,7553,7619,7684,7750,7803,7867,7945,8023,8082"}}]}]}