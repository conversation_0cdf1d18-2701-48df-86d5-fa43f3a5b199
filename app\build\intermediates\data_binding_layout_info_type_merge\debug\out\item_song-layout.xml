<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_song" modulePackage="com.musicplayer.pro" filePath="app\src\main\res\layout\item_song.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/item_song_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="91" endOffset="51"/></Target><Target id="@+id/albumCoverCard" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="10" startOffset="4" endLine="27" endOffset="39"/></Target><Target id="@+id/albumCover" view="ImageView"><Expressions/><location startLine="20" startOffset="8" endLine="25" endOffset="57"/></Target><Target id="@+id/songInfoLayout" view="LinearLayout"><Expressions/><location startLine="30" startOffset="4" endLine="63" endOffset="18"/></Target><Target id="@+id/songTitle" view="TextView"><Expressions/><location startLine="42" startOffset="8" endLine="51" endOffset="37"/></Target><Target id="@+id/artistName" view="TextView"><Expressions/><location startLine="53" startOffset="8" endLine="61" endOffset="38"/></Target><Target id="@+id/duration" view="TextView"><Expressions/><location startLine="66" startOffset="4" endLine="76" endOffset="27"/></Target><Target id="@+id/favoriteIcon" view="ImageView"><Expressions/><location startLine="79" startOffset="4" endLine="89" endOffset="51"/></Target></Targets></Layout>