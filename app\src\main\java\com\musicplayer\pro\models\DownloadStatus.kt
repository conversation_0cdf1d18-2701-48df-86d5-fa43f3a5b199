package com.musicplayer.pro.models

/**
 * حالات التحميل - مطابقة لـ DownloadStatus في Python
 */
enum class DownloadStatus {
    PENDING,        // في الانتظار
    DOWNLOADING,    // قيد التحميل
    PAUSED,         // متوقف مؤقتاً
    COMPLETED,      // مكتمل
    FAILED,         // فاشل
    CANCELLED       // ملغي
}

/**
 * امتدادات مفيدة لحالات التحميل
 */
fun DownloadStatus.getDisplayName(): String {
    return when (this) {
        DownloadStatus.PENDING -> "في الانتظار"
        DownloadStatus.DOWNLOADING -> "قيد التحميل"
        DownloadStatus.PAUSED -> "متوقف"
        DownloadStatus.COMPLETED -> "مكتمل"
        DownloadStatus.FAILED -> "فاشل"
        DownloadStatus.CANCELLED -> "ملغي"
    }
}

fun DownloadStatus.getColor(): Int {
    return when (this) {
        DownloadStatus.PENDING -> android.graphics.Color.GRAY
        DownloadStatus.DOWNLOADING -> android.graphics.Color.BLUE
        DownloadStatus.PAUSED -> android.graphics.Color.YELLOW
        DownloadStatus.COMPLETED -> android.graphics.Color.GREEN
        DownloadStatus.FAILED -> android.graphics.Color.RED
        DownloadStatus.CANCELLED -> android.graphics.Color.GRAY
    }
}
