package com.musicplayer.pro.models

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * نموذج بيانات قائمة التشغيل
 */
@Parcelize
data class Playlist(
    val id: Long = 0,
    val name: String = "",
    val description: String = "",
    val coverArt: String? = null,
    val dateCreated: Long = System.currentTimeMillis(),
    val dateModified: Long = System.currentTimeMillis(),
    val songCount: Int = 0,
    val totalDuration: Long = 0,
    val isSystemPlaylist: Boolean = false,
    val songs: List<Song> = emptyList()
) : Parcelable {
    
    /**
     * الحصول على المدة الإجمالية بتنسيق قابل للقراءة
     */
    fun getFormattedDuration(): String {
        val hours = totalDuration / 1000 / 3600
        val minutes = (totalDuration / 1000 % 3600) / 60
        
        return when {
            hours > 0 -> String.format("%d:%02d hours", hours, minutes)
            else -> String.format("%d minutes", minutes)
        }
    }
    
    /**
     * فحص ما إذا كانت القائمة فارغة
     */
    fun isEmpty(): Boolean = songs.isEmpty()
    
    /**
     * الحصول على أول أغنية في القائمة
     */
    fun getFirstSong(): Song? = songs.firstOrNull()
    
    /**
     * الحصول على آخر أغنية في القائمة
     */
    fun getLastSong(): Song? = songs.lastOrNull()
    
    /**
     * فحص ما إذا كانت الأغنية موجودة في القائمة
     */
    fun containsSong(song: Song): Boolean = songs.any { it.id == song.id }
    
    /**
     * الحصول على فهرس الأغنية في القائمة
     */
    fun getSongIndex(song: Song): Int = songs.indexOfFirst { it.id == song.id }
}

/**
 * أنواع قوائم التشغيل المختلفة
 */
enum class PlaylistType {
    USER_CREATED,
    FAVORITES,
    RECENTLY_PLAYED,
    MOST_PLAYED,
    RECENTLY_ADDED,
    DOWNLOADED
}



/**
 * أنماط التكرار
 */
enum class RepeatMode {
    OFF,
    ONE,
    ALL
}
