package com.musicplayer.pro.models

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * نموذج بيانات قائمة التشغيل
 */
@Parcelize
data class Playlist(
    val id: Long = 0,
    val name: String = "",
    val description: String = "",
    val coverArt: String? = null,
    val dateCreated: Long = System.currentTimeMillis(),
    val dateModified: Long = System.currentTimeMillis(),
    val songCount: Int = 0,
    val totalDuration: Long = 0,
    val isSystemPlaylist: Boolean = false,
    val songs: List<Song> = emptyList()
) : Parcelable {
    
    /**
     * الحصول على المدة الإجمالية بتنسيق قابل للقراءة
     */
    fun getFormattedDuration(): String {
        val hours = totalDuration / 1000 / 3600
        val minutes = (totalDuration / 1000 % 3600) / 60
        
        return when {
            hours > 0 -> String.format("%d:%02d hours", hours, minutes)
            else -> String.format("%d minutes", minutes)
        }
    }
    
    /**
     * فحص ما إذا كانت القائمة فارغة
     */
    fun isEmpty(): Boolean = songs.isEmpty()
    
    /**
     * الحصول على أول أغنية في القائمة
     */
    fun getFirstSong(): Song? = songs.firstOrNull()
    
    /**
     * الحصول على آخر أغنية في القائمة
     */
    fun getLastSong(): Song? = songs.lastOrNull()
    
    /**
     * فحص ما إذا كانت الأغنية موجودة في القائمة
     */
    fun containsSong(song: Song): Boolean = songs.any { it.id == song.id }
    
    /**
     * الحصول على فهرس الأغنية في القائمة
     */
    fun getSongIndex(song: Song): Int = songs.indexOfFirst { it.id == song.id }
}

/**
 * أنواع قوائم التشغيل المختلفة
 */
enum class PlaylistType {
    USER_CREATED,
    FAVORITES,
    RECENTLY_PLAYED,
    MOST_PLAYED,
    RECENTLY_ADDED,
    DOWNLOADED
}

/**
 * نموذج بيانات Download
 */
@Parcelize
data class Download(
    val id: String = "",
    val url: String = "",
    val title: String = "",
    val artist: String = "",
    val thumbnail: String? = null,
    val status: DownloadStatus = DownloadStatus.PENDING,
    val progress: Int = 0,
    val totalSize: Long = 0,
    val downloadedSize: Long = 0,
    val filePath: String = "",
    val quality: String = "best",
    val format: String = "mp3",
    val dateStarted: Long = System.currentTimeMillis(),
    val dateCompleted: Long? = null,
    val errorMessage: String? = null,
    val speed: String = "",
    val eta: String = ""
) : Parcelable {
    
    /**
     * الحصول على النسبة المئوية للتقدم
     */
    fun getProgressPercentage(): Int {
        return if (totalSize > 0) {
            ((downloadedSize * 100) / totalSize).toInt()
        } else {
            progress
        }
    }
    
    /**
     * الحصول على الحجم المحمل بتنسيق قابل للقراءة
     */
    fun getFormattedDownloadedSize(): String {
        return formatFileSize(downloadedSize)
    }
    
    /**
     * الحصول على الحجم الإجمالي بتنسيق قابل للقراءة
     */
    fun getFormattedTotalSize(): String {
        return formatFileSize(totalSize)
    }
    
    private fun formatFileSize(size: Long): String {
        return when {
            size < 1024 -> "$size B"
            size < 1024 * 1024 -> "${size / 1024} KB"
            else -> "${size / (1024 * 1024)} MB"
        }
    }
    
    /**
     * فحص ما إذا كان التحميل مكتمل
     */
    fun isCompleted(): Boolean = status == DownloadStatus.COMPLETED
    
    /**
     * فحص ما إذا كان التحميل فاشل
     */
    fun isFailed(): Boolean = status == DownloadStatus.FAILED
    
    /**
     * فحص ما إذا كان التحميل قيد التنفيذ
     */
    fun isInProgress(): Boolean = status == DownloadStatus.DOWNLOADING
}

/**
 * حالات التحميل المختلفة
 */
enum class DownloadStatus {
    PENDING,
    DOWNLOADING,
    PAUSED,
    COMPLETED,
    FAILED,
    CANCELLED
}

/**
 * أنماط التكرار
 */
enum class RepeatMode {
    OFF,
    ONE,
    ALL
}
