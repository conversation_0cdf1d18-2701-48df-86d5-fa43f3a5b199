from PIL import Image, ImageDraw, ImageFont
import os

# Create directories if they don't exist
os.makedirs('app/src/main/res/mipmap-hdpi', exist_ok=True)

# Create a simple icon
def create_icon(size, filename):
    # Create image with purple background
    img = Image.new('RGB', (size, size), color='#6200EE')
    draw = ImageDraw.Draw(img)
    
    # Draw a white circle for music note
    margin = size // 6
    draw.ellipse([margin, margin, size-margin, size-margin], fill='white')
    
    # Draw a smaller purple circle inside
    inner_margin = size // 3
    draw.ellipse([inner_margin, inner_margin, size-inner_margin, size-inner_margin], fill='#6200EE')
    
    # Save the image
    img.save(filename, 'PNG')
    print(f"Created {filename}")

# Create icons
create_icon(48, 'app/src/main/res/mipmap-hdpi/ic_launcher.png')
create_icon(48, 'app/src/main/res/mipmap-hdpi/ic_launcher_round.png')
