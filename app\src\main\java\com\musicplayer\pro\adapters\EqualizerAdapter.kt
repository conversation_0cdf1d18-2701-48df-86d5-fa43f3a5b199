package com.musicplayer.pro.adapters

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.SeekBar
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.musicplayer.pro.R
import com.musicplayer.pro.models.EqualizerBand

/**
 * محول قائمة نطاقات المعادل الصوتي
 */
class EqualizerAdapter(
    private val onBandLevelChanged: (EqualizerBand, Short) -> Unit
) : RecyclerView.Adapter<EqualizerAdapter.BandViewHolder>() {
    
    private var bands: List<EqualizerBand> = emptyList()
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BandViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_equalizer_band, parent, false)
        return BandViewHolder(view)
    }
    
    override fun onBindViewHolder(holder: BandViewHolder, position: Int) {
        val band = bands[position]
        holder.bind(band)
    }
    
    override fun getItemCount(): Int = bands.size
    
    /**
     * تحديث قائمة النطاقات
     */
    fun updateBands(newBands: List<EqualizerBand>) {
        bands = newBands
        notifyDataSetChanged()
    }
    
    inner class BandViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val frequencyLabel: TextView = itemView.findViewById(R.id.frequencyLabel)
        private val levelLabel: TextView = itemView.findViewById(R.id.levelLabel)
        private val levelSeekBar: SeekBar = itemView.findViewById(R.id.levelSeekBar)
        
        fun bind(band: EqualizerBand) {
            frequencyLabel.text = band.getFormattedFrequency()
            levelLabel.text = "${band.getLevelInDb()} dB"
            
            // إعداد SeekBar
            levelSeekBar.max = 100 // 0-100%
            levelSeekBar.progress = band.getLevelAsPercentage()
            
            levelSeekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
                override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                    if (fromUser) {
                        val newLevel = band.percentageToLevel(progress)
                        levelLabel.text = "${newLevel / 100.0f} dB"
                        onBandLevelChanged(band, newLevel)
                    }
                }
                
                override fun onStartTrackingTouch(seekBar: SeekBar?) {}
                override fun onStopTrackingTouch(seekBar: SeekBar?) {}
            })
        }
    }
}
