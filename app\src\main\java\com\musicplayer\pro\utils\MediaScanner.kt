package com.musicplayer.pro.utils

import android.content.ContentResolver
import android.content.Context
import android.database.Cursor
import android.net.Uri
import android.os.Environment
import android.provider.MediaStore
import android.util.Log
import com.musicplayer.pro.models.Song
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

/**
 * فاحص الوسائط للبحث التلقائي عن الأغاني في الجهاز
 */
class MediaScanner(private val context: Context) {

    companion object {
        private const val TAG = "MediaScanner"

        // أنواع الملفات الصوتية المدعومة
        private val SUPPORTED_AUDIO_FORMATS = arrayOf(
            "mp3", "m4a", "aac", "ogg", "wav", "flac", "wma", "opus", "3gp", "amr"
        )

        // الحد الأدنى لحجم الملف (1 MB)
        private const val MIN_FILE_SIZE = 1024 * 1024

        // الحد الأدنى لمدة الأغنية (30 ثانية)
        private const val MIN_DURATION = 30 * 1000
    }

    /**
     * فحص شامل للجهاز للبحث عن الأغاني
     */
    suspend fun scanForMusic(): List<Song> = withContext(Dispatchers.IO) {
        try {
            // فحص الأذونات أولاً
            if (!hasRequiredPermissions()) {
                Log.w(TAG, "الأذونات المطلوبة غير متوفرة")
                return@withContext emptyList()
            }

            Log.d(TAG, "بدء فحص الجهاز للبحث عن الأغاني...")

            // فقط استخدام MediaStore للأمان
            val songs = scanUsingMediaStore()

            Log.d(TAG, "تم العثور على ${songs.size} أغنية")
            songs

        } catch (e: Exception) {
            Log.e(TAG, "خطأ في فحص الجهاز: ${e.message}")
            emptyList()
        }
    }

    /**
     * فحص الأذونات المطلوبة
     */
    private fun hasRequiredPermissions(): Boolean {
        return if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
            androidx.core.content.ContextCompat.checkSelfPermission(
                context, android.Manifest.permission.READ_MEDIA_AUDIO
            ) == android.content.pm.PackageManager.PERMISSION_GRANTED
        } else {
            androidx.core.content.ContextCompat.checkSelfPermission(
                context, android.Manifest.permission.READ_EXTERNAL_STORAGE
            ) == android.content.pm.PackageManager.PERMISSION_GRANTED
        }
    }

    /**
     * فحص باستخدام MediaStore
     */
    private fun scanUsingMediaStore(): List<Song> {
        val songs = mutableListOf<Song>()

        try {
            val contentResolver: ContentResolver = context.contentResolver

            val projection = arrayOf(
                MediaStore.Audio.Media._ID,
                MediaStore.Audio.Media.TITLE,
                MediaStore.Audio.Media.ARTIST,
                MediaStore.Audio.Media.ALBUM,
                MediaStore.Audio.Media.DATA,
                MediaStore.Audio.Media.DURATION,
                MediaStore.Audio.Media.SIZE
            )

            val selection = "${MediaStore.Audio.Media.IS_MUSIC} = 1"
            val sortOrder = "${MediaStore.Audio.Media.TITLE} ASC"

            val cursor: Cursor? = contentResolver.query(
                MediaStore.Audio.Media.EXTERNAL_CONTENT_URI,
                projection,
                selection,
                null,
                sortOrder
            )

            cursor?.use {
                val idColumn = it.getColumnIndexOrThrow(MediaStore.Audio.Media._ID)
                val titleColumn = it.getColumnIndexOrThrow(MediaStore.Audio.Media.TITLE)
                val artistColumn = it.getColumnIndexOrThrow(MediaStore.Audio.Media.ARTIST)
                val albumColumn = it.getColumnIndexOrThrow(MediaStore.Audio.Media.ALBUM)
                val dataColumn = it.getColumnIndexOrThrow(MediaStore.Audio.Media.DATA)
                val durationColumn = it.getColumnIndexOrThrow(MediaStore.Audio.Media.DURATION)
                val sizeColumn = it.getColumnIndexOrThrow(MediaStore.Audio.Media.SIZE)

                while (it.moveToNext()) {
                    try {
                        val id = it.getLong(idColumn)
                        val title = it.getString(titleColumn) ?: "Unknown"
                        val artist = it.getString(artistColumn) ?: "Unknown Artist"
                        val album = it.getString(albumColumn) ?: "Unknown Album"
                        val path = it.getString(dataColumn)
                        val duration = it.getLong(durationColumn)
                        val size = it.getLong(sizeColumn)

                        // التحقق من وجود الملف والحد الأدنى للحجم والمدة
                        if (path != null && File(path).exists() &&
                            size >= MIN_FILE_SIZE && duration >= MIN_DURATION) {

                            val song = Song(
                                id = id,
                                title = title,
                                artist = artist,
                                album = album,
                                path = path,
                                duration = duration,
                                size = size,
                                albumArt = null
                            )

                            songs.add(song)
                        }
                    } catch (e: Exception) {
                        Log.w(TAG, "خطأ في قراءة بيانات الأغنية: ${e.message}")
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "خطأ في فحص MediaStore: ${e.message}")
        }

        Log.d(TAG, "MediaStore: تم العثور على ${songs.size} أغنية")
        return songs
    }

    /**
     * فحص سريع للحصول على عدد الأغاني
     */
    suspend fun getQuickSongCount(): Int = withContext(Dispatchers.IO) {
        try {
            if (!hasRequiredPermissions()) {
                return@withContext 0
            }

            val contentResolver: ContentResolver = context.contentResolver
            val cursor = contentResolver.query(
                MediaStore.Audio.Media.EXTERNAL_CONTENT_URI,
                arrayOf(MediaStore.Audio.Media._ID),
                "${MediaStore.Audio.Media.IS_MUSIC} = 1",
                null,
                null
            )

            val count = cursor?.count ?: 0
            cursor?.close()
            count
        } catch (e: Exception) {
            Log.e(TAG, "خطأ في الفحص السريع: ${e.message}")
            0
        }
    }
}
