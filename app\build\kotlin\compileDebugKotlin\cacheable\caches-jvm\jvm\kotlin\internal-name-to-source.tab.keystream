*com/musicplayer/pro/MusicPlayerApplication&com/musicplayer/pro/SimpleMainActivity com/musicplayer/pro/MainActivity8com/musicplayer/pro/MainActivity$setupServiceCallbacks$18com/musicplayer/pro/MainActivity$setupServiceCallbacks$2*com/musicplayer/pro/MainActivity$Companion1com/musicplayer/pro/MainActivity$MainPagerAdapter4com/musicplayer/pro/MainActivity$serviceConnection$1(com/musicplayer/pro/adapters/SongAdapter7com/musicplayer/pro/adapters/SongAdapter$SongViewHolder.com/musicplayer/pro/fragments/DownloadFragment*com/musicplayer/pro/fragments/MainFragment>com/musicplayer/pro/fragments/MainFragment$setupRecyclerView$1>com/musicplayer/pro/fragments/MainFragment$setupRecyclerView$2>com/musicplayer/pro/fragments/MainFragment$setupRecyclerView$3;com/musicplayer/pro/fragments/MainFragment$setupObservers$1;com/musicplayer/pro/fragments/MainFragment$setupObservers$2;com/musicplayer/pro/fragments/MainFragment$setupObservers$3;com/musicplayer/pro/fragments/MainFragment$setupObservers$4Lcom/musicplayer/pro/fragments/MainFragment$sam$androidx_lifecycle_Observer$00com/musicplayer/pro/fragments/NowPlayingFragmentAcom/musicplayer/pro/fragments/NowPlayingFragment$setupObservers$1Acom/musicplayer/pro/fragments/NowPlayingFragment$setupObservers$2Acom/musicplayer/pro/fragments/NowPlayingFragment$setupObservers$3Acom/musicplayer/pro/fragments/NowPlayingFragment$setupObservers$4Acom/musicplayer/pro/fragments/NowPlayingFragment$setupObservers$5Acom/musicplayer/pro/fragments/NowPlayingFragment$setupObservers$6Acom/musicplayer/pro/fragments/NowPlayingFragment$setupListeners$7Rcom/musicplayer/pro/fragments/NowPlayingFragment$sam$androidx_lifecycle_Observer$0=com/musicplayer/pro/fragments/NowPlayingFragment$WhenMappings#com/musicplayer/pro/models/Playlist+com/musicplayer/pro/models/Playlist$Creator'com/musicplayer/pro/models/PlaylistType#com/musicplayer/pro/models/Download+com/musicplayer/pro/models/Download$Creator)com/musicplayer/pro/models/DownloadStatus%com/musicplayer/pro/models/RepeatModecom/musicplayer/pro/models/Song'com/musicplayer/pro/models/Song$Creator0com/musicplayer/pro/repositories/MusicRepository)com/musicplayer/pro/services/MusicService5com/musicplayer/pro/services/MusicService$MusicBinder%com/musicplayer/pro/utils/ImageLoader&com/musicplayer/pro/utils/MediaScanner+com/musicplayer/pro/utils/PermissionManager&com/musicplayer/pro/utils/ThemeManager-com/musicplayer/pro/viewmodels/MusicViewModel9com/musicplayer/pro/viewmodels/MusicViewModel$loadSongs$1=com/musicplayer/pro/viewmodels/MusicViewModel$loadPlaylists$1>com/musicplayer/pro/viewmodels/MusicViewModel$toggleFavorite$1;com/musicplayer/pro/viewmodels/MusicViewModel$searchSongs$1>com/musicplayer/pro/viewmodels/MusicViewModel$createPlaylist$1>com/musicplayer/pro/viewmodels/MusicViewModel$deletePlaylist$1:com/musicplayer/pro/viewmodels/MusicViewModel$WhenMappings,com/musicplayer/pro/adapters/DownloadAdapter?com/musicplayer/pro/adapters/DownloadAdapter$DownloadViewHolderLcom/musicplayer/pro/adapters/DownloadAdapter$DownloadViewHolder$WhenMappingsBcom/musicplayer/pro/fragments/DownloadFragment$setupRecyclerView$1Bcom/musicplayer/pro/fragments/DownloadFragment$setupRecyclerView$2Bcom/musicplayer/pro/fragments/DownloadFragment$setupRecyclerView$3Bcom/musicplayer/pro/fragments/DownloadFragment$setupRecyclerView$4Bcom/musicplayer/pro/fragments/DownloadFragment$setupRecyclerView$5Bcom/musicplayer/pro/fragments/DownloadFragment$setupRecyclerView$6?com/musicplayer/pro/fragments/DownloadFragment$setupObservers$1?com/musicplayer/pro/fragments/DownloadFragment$setupObservers$2?com/musicplayer/pro/fragments/DownloadFragment$setupObservers$3Fcom/musicplayer/pro/fragments/DownloadFragment$showAddDownloadDialog$3Pcom/musicplayer/pro/fragments/DownloadFragment$sam$androidx_lifecycle_Observer$0;com/musicplayer/pro/fragments/DownloadFragment$WhenMappings,com/musicplayer/pro/managers/DownloadManager<com/musicplayer/pro/managers/DownloadManager$startDownload$1?com/musicplayer/pro/managers/DownloadManager$simulateDownload$1?com/musicplayer/pro/managers/DownloadManager$extractVideoInfo$16com/musicplayer/pro/managers/DownloadManager$VideoInfo&com/musicplayer/pro/utils/ExtensionsKtAcom/musicplayer/pro/utils/ExtensionsKt$setOnTextChangedListener$10com/musicplayer/pro/viewmodels/DownloadViewModel@com/musicplayer/pro/viewmodels/DownloadViewModel$loadDownloads$1@com/musicplayer/pro/viewmodels/DownloadViewModel$startDownload$1@com/musicplayer/pro/viewmodels/DownloadViewModel$pauseDownload$1Acom/musicplayer/pro/viewmodels/DownloadViewModel$resumeDownload$1Acom/musicplayer/pro/viewmodels/DownloadViewModel$cancelDownload$1@com/musicplayer/pro/viewmodels/DownloadViewModel$retryDownload$1Acom/musicplayer/pro/viewmodels/DownloadViewModel$deleteDownload$1Ecom/musicplayer/pro/viewmodels/DownloadViewModel$playDownloadedFile$13com/musicplayer/pro/services/MusicService$Companion6com/musicplayer/pro/services/MusicService$WhenMappingsBcom/musicplayer/pro/services/MusicService$updatePositionRunnable$1$com/musicplayer/pro/SettingsActivity5com/musicplayer/pro/SettingsActivity$SettingsFragmentDcom/musicplayer/pro/SettingsActivity$SettingsFragment$createBackup$1Fcom/musicplayer/pro/SettingsActivity$SettingsFragment$createBackup$1$1Ecom/musicplayer/pro/SettingsActivity$SettingsFragment$restoreBackup$1Gcom/musicplayer/pro/SettingsActivity$SettingsFragment$restoreBackup$1$10com/musicplayer/pro/managers/AudioEffectsManager*com/musicplayer/pro/managers/BackupManager9com/musicplayer/pro/managers/BackupManager$createBackup$2;com/musicplayer/pro/managers/BackupManager$createBackup$2$2;com/musicplayer/pro/managers/BackupManager$createBackup$2$3:com/musicplayer/pro/managers/BackupManager$restoreBackup$2<com/musicplayer/pro/managers/BackupManager$restoreBackup$2$1<com/musicplayer/pro/managers/BackupManager$restoreBackup$2$2<com/musicplayer/pro/managers/BackupManager$restoreBackup$2$3Wcom/musicplayer/pro/managers/BackupManager$getBackupFiles$$inlined$sortedByDescending$1)com/musicplayer/pro/managers/CacheManager6com/musicplayer/pro/managers/CacheManager$WhenMappings&com/musicplayer/pro/managers/CacheType)com/musicplayer/pro/managers/CacheDetails%com/musicplayer/pro/EqualizerActivity9com/musicplayer/pro/EqualizerActivity$setupRecyclerView$1;com/musicplayer/pro/EqualizerActivity$setupEffectControls$1;com/musicplayer/pro/EqualizerActivity$setupEffectControls$2-com/musicplayer/pro/adapters/EqualizerAdapter<com/musicplayer/pro/adapters/EqualizerAdapter$BandViewHolderCcom/musicplayer/pro/adapters/EqualizerAdapter$BandViewHolder$bind$1(com/musicplayer/pro/models/EqualizerBand5com/musicplayer/pro/MainActivity$startAutoMusicScan$19com/musicplayer/pro/fragments/MainFragment$scanReceiver$15com/musicplayer/pro/utils/MediaScanner$scanForMusic$2Kcom/musicplayer/pro/utils/MediaScanner$removeDuplicates$$inlined$sortedBy$1:com/musicplayer/pro/utils/MediaScanner$getQuickSongCount$20com/musicplayer/pro/utils/MediaScanner$Companion:com/musicplayer/pro/MainActivity$setupServiceCallbacks$1$1:com/musicplayer/pro/MainActivity$setupServiceCallbacks$1$2@com/musicplayer/pro/viewmodels/MusicViewModel$bindMusicService$1@com/musicplayer/pro/viewmodels/MusicViewModel$bindMusicService$27com/musicplayer/pro/MainActivity$startProgressUpdates$1@com/musicplayer/pro/viewmodels/MusicViewModel$bindMusicService$3                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  