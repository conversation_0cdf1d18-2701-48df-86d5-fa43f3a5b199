package com.musicplayer.pro.fragments

import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.os.Bundle
import android.view.*
import android.view.animation.AccelerateDecelerateInterpolator
import android.widget.*
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.musicplayer.pro.R
import com.musicplayer.pro.viewmodels.LoadingViewModel
import com.musicplayer.pro.views.LoadingProgressView
import kotlinx.coroutines.*

/**
 * شاشة التحميل
 * تعرض شاشة البداية مع شريط التقدم وأيقونة التطبيق
 */
class LoadingFragment : Fragment() {
    
    companion object {
        private const val TAG = "LoadingFragment"
        private const val LOADING_DURATION = 3000L // 3 ثوانٍ
    }
    
    // ViewModel
    private lateinit var viewModel: LoadingViewModel
    
    // UI Components
    private lateinit var appIconImageView: ImageView
    private lateinit var appNameTextView: TextView
    private lateinit var loadingProgressView: LoadingProgressView
    private lateinit var loadingTextView: TextView
    private lateinit var progressBar: ProgressBar
    
    // Animation
    private var iconAnimator: ObjectAnimator? = null
    private var textAnimator: ObjectAnimator? = null
    
    // Coroutines
    private val fragmentScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    
    // Loading steps
    private val loadingSteps = listOf(
        "تهيئة التطبيق...",
        "فحص الأذونات...",
        "مسح الملفات الصوتية...",
        "تحميل قاعدة البيانات...",
        "إعداد مشغل الموسيقى...",
        "اكتمل التحميل!"
    )
    
    private var currentStep = 0
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_loading, container, false)
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        // تهيئة ViewModel
        viewModel = ViewModelProvider(this)[LoadingViewModel::class.java]
        
        // تهيئة UI
        initializeViews(view)
        setupAnimations()
        
        // مراقبة البيانات
        observeData()
        
        // بدء عملية التحميل
        startLoading()
    }
    
    /**
     * تهيئة العناصر
     */
    private fun initializeViews(view: View) {
        appIconImageView = view.findViewById(R.id.appIconImageView)
        appNameTextView = view.findViewById(R.id.appNameTextView)
        loadingProgressView = view.findViewById(R.id.loadingProgressView)
        loadingTextView = view.findViewById(R.id.loadingTextView)
        progressBar = view.findViewById(R.id.progressBar)
        
        // تعيين النصوص الأولية
        appNameTextView.text = getString(R.string.app_name)
        loadingTextView.text = loadingSteps[0]
        progressBar.max = 100
        progressBar.progress = 0
    }
    
    /**
     * إعداد الحركات
     */
    private fun setupAnimations() {
        // حركة الأيقونة (دوران وتكبير/تصغير)
        setupIconAnimation()
        
        // حركة النص (تلاشي)
        setupTextAnimation()
        
        // حركة شريط التقدم المخصص
        loadingProgressView.startAnimation()
    }
    
    /**
     * إعداد حركة الأيقونة
     */
    private fun setupIconAnimation() {
        // حركة دوران مستمرة
        iconAnimator = ObjectAnimator.ofFloat(appIconImageView, "rotation", 0f, 360f).apply {
            duration = 2000
            repeatCount = ValueAnimator.INFINITE
            interpolator = AccelerateDecelerateInterpolator()
            start()
        }
        
        // حركة تكبير وتصغير
        val scaleXAnimator = ObjectAnimator.ofFloat(appIconImageView, "scaleX", 1f, 1.1f, 1f).apply {
            duration = 1500
            repeatCount = ValueAnimator.INFINITE
            interpolator = AccelerateDecelerateInterpolator()
        }
        
        val scaleYAnimator = ObjectAnimator.ofFloat(appIconImageView, "scaleY", 1f, 1.1f, 1f).apply {
            duration = 1500
            repeatCount = ValueAnimator.INFINITE
            interpolator = AccelerateDecelerateInterpolator()
        }
        
        scaleXAnimator.start()
        scaleYAnimator.start()
    }
    
    /**
     * إعداد حركة النص
     */
    private fun setupTextAnimation() {
        textAnimator = ObjectAnimator.ofFloat(loadingTextView, "alpha", 0.5f, 1f, 0.5f).apply {
            duration = 1000
            repeatCount = ValueAnimator.INFINITE
            interpolator = AccelerateDecelerateInterpolator()
            start()
        }
    }
    
    /**
     * مراقبة البيانات
     */
    private fun observeData() {
        // مراقبة تقدم التحميل
        viewModel.loadingProgress.observe(viewLifecycleOwner) { progress ->
            updateProgress(progress)
        }
        
        // مراقبة خطوة التحميل الحالية
        viewModel.currentStep.observe(viewLifecycleOwner) { step ->
            updateLoadingStep(step)
        }
        
        // مراقبة اكتمال التحميل
        viewModel.isLoadingComplete.observe(viewLifecycleOwner) { isComplete ->
            if (isComplete) {
                onLoadingComplete()
            }
        }
        
        // مراقبة الأخطاء
        viewModel.error.observe(viewLifecycleOwner) { error ->
            if (error.isNotEmpty()) {
                showError(error)
            }
        }
    }
    
    /**
     * بدء عملية التحميل
     */
    private fun startLoading() {
        fragmentScope.launch {
            // محاكاة خطوات التحميل
            for (i in loadingSteps.indices) {
                currentStep = i
                
                // تحديث النص
                loadingTextView.text = loadingSteps[i]
                
                // تحديث التقدم
                val progress = ((i + 1) * 100) / loadingSteps.size
                updateProgress(progress)
                
                // انتظار قبل الخطوة التالية
                delay(LOADING_DURATION / loadingSteps.size)
                
                // تنفيذ خطوة التحميل الفعلية
                when (i) {
                    0 -> viewModel.initializeApp()
                    1 -> viewModel.checkPermissions()
                    2 -> viewModel.scanAudioFiles()
                    3 -> viewModel.loadDatabase()
                    4 -> viewModel.setupMusicPlayer()
                    5 -> viewModel.completeLoading()
                }
            }
        }
    }
    
    /**
     * تحديث التقدم
     */
    private fun updateProgress(progress: Int) {
        // تحديث شريط التقدم العادي
        progressBar.progress = progress
        
        // تحديث شريط التقدم المخصص
        loadingProgressView.setProgress(progress)
        
        // تحديث ViewModel
        viewModel.updateProgress(progress)
    }
    
    /**
     * تحديث خطوة التحميل
     */
    private fun updateLoadingStep(step: String) {
        loadingTextView.text = step
        
        // إضافة حركة تلاشي عند تغيير النص
        loadingTextView.animate()
            .alpha(0f)
            .setDuration(200)
            .withEndAction {
                loadingTextView.text = step
                loadingTextView.animate()
                    .alpha(1f)
                    .setDuration(200)
                    .start()
            }
            .start()
    }
    
    /**
     * عند اكتمال التحميل
     */
    private fun onLoadingComplete() {
        // إيقاف الحركات
        stopAnimations()
        
        // حركة انتهاء جميلة
        fragmentScope.launch {
            // تكبير الأيقونة
            appIconImageView.animate()
                .scaleX(1.2f)
                .scaleY(1.2f)
                .setDuration(300)
                .start()
            
            delay(300)
            
            // تلاشي تدريجي
            view?.animate()
                ?.alpha(0f)
                ?.setDuration(500)
                ?.withEndAction {
                    // إشعار النشاط الرئيسي بانتهاء التحميل
                    (activity as? LoadingCompleteListener)?.onLoadingComplete()
                }
                ?.start()
        }
    }
    
    /**
     * عرض خطأ
     */
    private fun showError(error: String) {
        loadingTextView.text = "خطأ: $error"
        loadingTextView.setTextColor(resources.getColor(R.color.error_color, null))
        
        // إيقاف الحركات
        stopAnimations()
        
        // عرض زر إعادة المحاولة
        showRetryButton()
    }
    
    /**
     * عرض زر إعادة المحاولة
     */
    private fun showRetryButton() {
        val retryButton = Button(requireContext()).apply {
            text = getString(R.string.retry)
            setOnClickListener {
                retryLoading()
            }
        }
        
        // إضافة الزر للتخطيط
        (view as? ViewGroup)?.addView(retryButton)
    }
    
    /**
     * إعادة المحاولة
     */
    private fun retryLoading() {
        // إعادة تعيين الحالة
        currentStep = 0
        progressBar.progress = 0
        loadingProgressView.setProgress(0)
        loadingTextView.setTextColor(resources.getColor(R.color.text_primary, null))
        
        // إعادة بدء الحركات
        setupAnimations()
        
        // إعادة بدء التحميل
        startLoading()
    }
    
    /**
     * إيقاف الحركات
     */
    private fun stopAnimations() {
        iconAnimator?.cancel()
        textAnimator?.cancel()
        loadingProgressView.stopAnimation()
        
        // إيقاف جميع الحركات الجارية
        appIconImageView.clearAnimation()
        loadingTextView.clearAnimation()
    }
    
    /**
     * تغيير أيقونة التطبيق عشوائياً
     */
    private fun changeAppIcon() {
        val icons = arrayOf(
            R.drawable.app_icon_1,
            R.drawable.app_icon_2,
            R.drawable.app_icon_3,
            R.drawable.app_icon_4
        )
        
        val randomIcon = icons.random()
        appIconImageView.setImageResource(randomIcon)
    }
    
    override fun onResume() {
        super.onResume()
        // تغيير الأيقونة عشوائياً عند العودة للشاشة
        changeAppIcon()
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        
        // تنظيف الموارد
        stopAnimations()
        fragmentScope.cancel()
    }
    
    /**
     * واجهة للإشعار بانتهاء التحميل
     */
    interface LoadingCompleteListener {
        fun onLoadingComplete()
    }
}
