// Generated by view binder compiler. Do not edit!
package com.musicplayer.pro.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.SeekBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.musicplayer.pro.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemEqualizerBandBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final TextView frequencyLabel;

  @NonNull
  public final TextView levelLabel;

  @NonNull
  public final SeekBar levelSeekBar;

  private ItemEqualizerBandBinding(@NonNull CardView rootView, @NonNull TextView frequencyLabel,
      @NonNull TextView levelLabel, @NonNull SeekBar levelSeekBar) {
    this.rootView = rootView;
    this.frequencyLabel = frequencyLabel;
    this.levelLabel = levelLabel;
    this.levelSeekBar = levelSeekBar;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemEqualizerBandBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemEqualizerBandBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_equalizer_band, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemEqualizerBandBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.frequencyLabel;
      TextView frequencyLabel = ViewBindings.findChildViewById(rootView, id);
      if (frequencyLabel == null) {
        break missingId;
      }

      id = R.id.levelLabel;
      TextView levelLabel = ViewBindings.findChildViewById(rootView, id);
      if (levelLabel == null) {
        break missingId;
      }

      id = R.id.levelSeekBar;
      SeekBar levelSeekBar = ViewBindings.findChildViewById(rootView, id);
      if (levelSeekBar == null) {
        break missingId;
      }

      return new ItemEqualizerBandBinding((CardView) rootView, frequencyLabel, levelLabel,
          levelSeekBar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
