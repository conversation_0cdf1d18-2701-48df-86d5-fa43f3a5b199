package com.musicplayer.pro.managers

import android.content.Context
import android.content.SharedPreferences
import androidx.preference.PreferenceManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.text.SimpleDateFormat
import java.util.*

/**
 * مدير النسخ الاحتياطية - مطابق لميزات النسخ الاحتياطي في Python
 */
class BackupManager(private val context: Context) {
    
    private val backupDir = File(context.getExternalFilesDir(null), "Backups")
    
    init {
        if (!backupDir.exists()) {
            backupDir.mkdirs()
        }
    }
    
    /**
     * إنشاء نسخة احتياطية
     */
    suspend fun createBackup(callback: (Boolean) -> Unit) {
        withContext(Dispatchers.IO) {
            try {
                val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
                val backupFile = File(backupDir, "backup_$timestamp.json")
                
                val backupData = JSONObject().apply {
                    // نسخ الإعدادات
                    put("preferences", getPreferencesJson())
                    
                    // نسخ قوائم التشغيل (سيتم تنفيذها لاحقاً)
                    put("playlists", getPlaylistsJson())
                    
                    // نسخ المفضلة
                    put("favorites", getFavoritesJson())
                    
                    // معلومات النسخة الاحتياطية
                    put("backup_info", JSONObject().apply {
                        put("timestamp", timestamp)
                        put("version", getAppVersion())
                        put("device", getDeviceInfo())
                    })
                }
                
                FileOutputStream(backupFile).use { output ->
                    output.write(backupData.toString(2).toByteArray())
                }
                
                withContext(Dispatchers.Main) {
                    callback(true)
                }
                
            } catch (e: Exception) {
                e.printStackTrace()
                withContext(Dispatchers.Main) {
                    callback(false)
                }
            }
        }
    }
    
    /**
     * استعادة النسخة الاحتياطية
     */
    suspend fun restoreBackup(callback: (Boolean) -> Unit) {
        withContext(Dispatchers.IO) {
            try {
                val latestBackup = getLatestBackupFile()
                if (latestBackup == null) {
                    withContext(Dispatchers.Main) {
                        callback(false)
                    }
                    return@withContext
                }
                
                val backupContent = FileInputStream(latestBackup).use { input ->
                    input.readBytes().toString(Charsets.UTF_8)
                }
                
                val backupData = JSONObject(backupContent)
                
                // استعادة الإعدادات
                restorePreferences(backupData.optJSONObject("preferences"))
                
                // استعادة قوائم التشغيل
                restorePlaylists(backupData.optJSONObject("playlists"))
                
                // استعادة المفضلة
                restoreFavorites(backupData.optJSONObject("favorites"))
                
                withContext(Dispatchers.Main) {
                    callback(true)
                }
                
            } catch (e: Exception) {
                e.printStackTrace()
                withContext(Dispatchers.Main) {
                    callback(false)
                }
            }
        }
    }
    
    /**
     * الحصول على إعدادات التطبيق كـ JSON
     */
    private fun getPreferencesJson(): JSONObject {
        val prefs = PreferenceManager.getDefaultSharedPreferences(context)
        val prefsJson = JSONObject()
        
        prefs.all.forEach { (key, value) ->
            when (value) {
                is String -> prefsJson.put(key, value)
                is Boolean -> prefsJson.put(key, value)
                is Int -> prefsJson.put(key, value)
                is Long -> prefsJson.put(key, value)
                is Float -> prefsJson.put(key, value)
            }
        }
        
        return prefsJson
    }
    
    /**
     * الحصول على قوائم التشغيل كـ JSON
     */
    private fun getPlaylistsJson(): JSONObject {
        // سيتم تنفيذها عند إضافة قاعدة البيانات
        return JSONObject()
    }
    
    /**
     * الحصول على المفضلة كـ JSON
     */
    private fun getFavoritesJson(): JSONObject {
        // سيتم تنفيذها عند إضافة قاعدة البيانات
        return JSONObject()
    }
    
    /**
     * استعادة الإعدادات
     */
    private fun restorePreferences(prefsJson: JSONObject?) {
        if (prefsJson == null) return
        
        val prefs = PreferenceManager.getDefaultSharedPreferences(context)
        val editor = prefs.edit()
        
        prefsJson.keys().forEach { key ->
            when (val value = prefsJson.get(key)) {
                is String -> editor.putString(key, value)
                is Boolean -> editor.putBoolean(key, value)
                is Int -> editor.putInt(key, value)
                is Long -> editor.putLong(key, value)
                is Double -> editor.putFloat(key, value.toFloat())
            }
        }
        
        editor.apply()
    }
    
    /**
     * استعادة قوائم التشغيل
     */
    private fun restorePlaylists(playlistsJson: JSONObject?) {
        // سيتم تنفيذها عند إضافة قاعدة البيانات
    }
    
    /**
     * استعادة المفضلة
     */
    private fun restoreFavorites(favoritesJson: JSONObject?) {
        // سيتم تنفيذها عند إضافة قاعدة البيانات
    }
    
    /**
     * الحصول على أحدث ملف نسخة احتياطية
     */
    private fun getLatestBackupFile(): File? {
        val backupFiles = backupDir.listFiles { file ->
            file.name.startsWith("backup_") && file.name.endsWith(".json")
        }
        
        return backupFiles?.maxByOrNull { it.lastModified() }
    }
    
    /**
     * الحصول على إصدار التطبيق
     */
    private fun getAppVersion(): String {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            packageInfo.versionName
        } catch (e: Exception) {
            "Unknown"
        }
    }
    
    /**
     * الحصول على معلومات الجهاز
     */
    private fun getDeviceInfo(): String {
        return "${android.os.Build.MANUFACTURER} ${android.os.Build.MODEL}"
    }
    
    /**
     * الحصول على قائمة النسخ الاحتياطية
     */
    fun getBackupFiles(): List<File> {
        return backupDir.listFiles { file ->
            file.name.startsWith("backup_") && file.name.endsWith(".json")
        }?.sortedByDescending { it.lastModified() } ?: emptyList()
    }
    
    /**
     * حذف نسخة احتياطية
     */
    fun deleteBackup(file: File): Boolean {
        return try {
            file.delete()
        } catch (e: Exception) {
            false
        }
    }
}
