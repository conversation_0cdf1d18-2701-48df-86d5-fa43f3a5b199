<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    android:background="@color/surface_primary">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- صورة مصغرة -->
        <androidx.cardview.widget.CardView
            android:id="@+id/thumbnailCard"
            android:layout_width="60dp"
            android:layout_height="60dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/downloadThumbnail"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:src="@drawable/default_album_cover" />

        </androidx.cardview.widget.CardView>

        <!-- معلومات التحميل -->
        <LinearLayout
            android:id="@+id/downloadInfoLayout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="8dp"
            android:orientation="vertical"
            app:layout_constraintEnd_toStartOf="@+id/actionButton"
            app:layout_constraintStart_toEndOf="@+id/thumbnailCard"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/downloadTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="2"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold"
                tools:text="YouTube Video Title" />

            <TextView
                android:id="@+id/downloadArtist"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:ellipsize="end"
                android:singleLine="true"
                android:textColor="@color/text_secondary"
                android:textSize="14sp"
                tools:text="Channel Name" />

            <TextView
                android:id="@+id/downloadStatus"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:textColor="@color/accent_primary"
                android:textSize="12sp"
                android:textStyle="bold"
                tools:text="جاري التحميل" />

        </LinearLayout>

        <!-- أزرار العمل -->
        <ImageButton
            android:id="@+id/actionButton"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginEnd="8dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/play_pause"
            android:src="@drawable/ic_pause"
            android:tint="@color/accent_primary"
            app:layout_constraintEnd_toStartOf="@+id/deleteButton"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageButton
            android:id="@+id/deleteButton"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/delete"
            android:src="@drawable/ic_delete"
            android:tint="@color/error"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- شريط التقدم -->
        <ProgressBar
            android:id="@+id/downloadProgress"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="0dp"
            android:layout_height="4dp"
            android:layout_marginTop="12dp"
            android:progressTint="@color/accent_primary"
            android:progressBackgroundTint="@color/background_secondary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/downloadInfoLayout"
            tools:progress="45" />

        <!-- معلومات التقدم -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/downloadProgress">

            <TextView
                android:id="@+id/progressText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_secondary"
                android:textSize="12sp"
                tools:text="45%" />

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />

            <TextView
                android:id="@+id/downloadSize"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_secondary"
                android:textSize="12sp"
                tools:text="2.3 MB / 5.1 MB" />

        </LinearLayout>

        <!-- معلومات السرعة والوقت -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/downloadProgress">

            <TextView
                android:id="@+id/downloadSpeed"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_tertiary"
                android:textSize="11sp"
                tools:text="1.2 MB/s" />

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />

            <TextView
                android:id="@+id/downloadEta"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_tertiary"
                android:textSize="11sp"
                tools:text="2m 30s" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>
