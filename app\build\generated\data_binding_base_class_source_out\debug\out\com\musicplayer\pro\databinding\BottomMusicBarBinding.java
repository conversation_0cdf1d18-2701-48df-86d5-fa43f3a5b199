// Generated by view binder compiler. Do not edit!
package com.musicplayer.pro.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.musicplayer.pro.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class BottomMusicBarBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ImageButton btnMiniPlayPause;

  @NonNull
  public final ImageView miniAlbumCover;

  @NonNull
  public final CardView miniAlbumCoverCard;

  @NonNull
  public final TextView miniArtistName;

  @NonNull
  public final ProgressBar miniProgressBar;

  @NonNull
  public final TextView miniSongTitle;

  @NonNull
  public final View playingIndicator;

  @NonNull
  public final TextView qualityIndicator;

  @NonNull
  public final LinearLayout songInfoLayout;

  private BottomMusicBarBinding(@NonNull ConstraintLayout rootView,
      @NonNull ImageButton btnMiniPlayPause, @NonNull ImageView miniAlbumCover,
      @NonNull CardView miniAlbumCoverCard, @NonNull TextView miniArtistName,
      @NonNull ProgressBar miniProgressBar, @NonNull TextView miniSongTitle,
      @NonNull View playingIndicator, @NonNull TextView qualityIndicator,
      @NonNull LinearLayout songInfoLayout) {
    this.rootView = rootView;
    this.btnMiniPlayPause = btnMiniPlayPause;
    this.miniAlbumCover = miniAlbumCover;
    this.miniAlbumCoverCard = miniAlbumCoverCard;
    this.miniArtistName = miniArtistName;
    this.miniProgressBar = miniProgressBar;
    this.miniSongTitle = miniSongTitle;
    this.playingIndicator = playingIndicator;
    this.qualityIndicator = qualityIndicator;
    this.songInfoLayout = songInfoLayout;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static BottomMusicBarBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static BottomMusicBarBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.bottom_music_bar, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static BottomMusicBarBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnMiniPlayPause;
      ImageButton btnMiniPlayPause = ViewBindings.findChildViewById(rootView, id);
      if (btnMiniPlayPause == null) {
        break missingId;
      }

      id = R.id.miniAlbumCover;
      ImageView miniAlbumCover = ViewBindings.findChildViewById(rootView, id);
      if (miniAlbumCover == null) {
        break missingId;
      }

      id = R.id.miniAlbumCoverCard;
      CardView miniAlbumCoverCard = ViewBindings.findChildViewById(rootView, id);
      if (miniAlbumCoverCard == null) {
        break missingId;
      }

      id = R.id.miniArtistName;
      TextView miniArtistName = ViewBindings.findChildViewById(rootView, id);
      if (miniArtistName == null) {
        break missingId;
      }

      id = R.id.miniProgressBar;
      ProgressBar miniProgressBar = ViewBindings.findChildViewById(rootView, id);
      if (miniProgressBar == null) {
        break missingId;
      }

      id = R.id.miniSongTitle;
      TextView miniSongTitle = ViewBindings.findChildViewById(rootView, id);
      if (miniSongTitle == null) {
        break missingId;
      }

      id = R.id.playingIndicator;
      View playingIndicator = ViewBindings.findChildViewById(rootView, id);
      if (playingIndicator == null) {
        break missingId;
      }

      id = R.id.qualityIndicator;
      TextView qualityIndicator = ViewBindings.findChildViewById(rootView, id);
      if (qualityIndicator == null) {
        break missingId;
      }

      id = R.id.songInfoLayout;
      LinearLayout songInfoLayout = ViewBindings.findChildViewById(rootView, id);
      if (songInfoLayout == null) {
        break missingId;
      }

      return new BottomMusicBarBinding((ConstraintLayout) rootView, btnMiniPlayPause,
          miniAlbumCover, miniAlbumCoverCard, miniArtistName, miniProgressBar, miniSongTitle,
          playingIndicator, qualityIndicator, songInfoLayout);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
