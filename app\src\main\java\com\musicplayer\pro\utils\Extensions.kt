package com.musicplayer.pro.utils

import android.text.Editable
import android.text.TextWatcher
import android.widget.EditText

/**
 * Extension functions مفيدة
 */

/**
 * مستمع تغيير النص المبسط
 */
fun EditText.setOnTextChangedListener(onTextChanged: (CharSequence?) -> Unit) {
    this.addTextChangedListener(object : TextWatcher {
        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
        
        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            onTextChanged(s)
        }
        
        override fun afterTextChanged(s: Editable?) {}
    })
}
