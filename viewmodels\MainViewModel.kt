package com.musicplayer.pro.viewmodels

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.musicplayer.pro.models.*
import com.musicplayer.pro.repositories.MusicRepository
import com.musicplayer.pro.repositories.PlaylistRepository
import com.musicplayer.pro.services.MusicService
import com.musicplayer.pro.utils.AudioScanner
import kotlinx.coroutines.launch

/**
 * ViewModel للشاشة الرئيسية
 * يدير البيانات والعمليات الخاصة بالأغاني والألبومات والفنانين وقوائم التشغيل
 */
class MainViewModel(application: Application) : AndroidViewModel(application) {
    
    // Repositories
    private val musicRepository = MusicRepository(application)
    private val playlistRepository = PlaylistRepository(application)
    private val audioScanner = AudioScanner(application)
    
    // LiveData للأغاني
    private val _songs = MutableLiveData<List<Song>>()
    val songs: LiveData<List<Song>> = _songs
    
    // LiveData للألبومات
    private val _albums = MutableLiveData<List<Album>>()
    val albums: LiveData<List<Album>> = _albums
    
    // LiveData للفنانين
    private val _artists = MutableLiveData<List<Artist>>()
    val artists: LiveData<List<Artist>> = _artists
    
    // LiveData لقوائم التشغيل
    private val _playlists = MutableLiveData<List<Playlist>>()
    val playlists: LiveData<List<Playlist>> = _playlists
    
    // LiveData لنتائج البحث
    private val _searchResults = MutableLiveData<SearchResults>()
    val searchResults: LiveData<SearchResults> = _searchResults
    
    // LiveData لحالة التحميل
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    // LiveData للرسائل
    private val _message = MutableLiveData<String>()
    val message: LiveData<String> = _message
    
    // خدمة الموسيقى
    private var musicService: MusicService? = null
    
    init {
        // تحميل البيانات الأولية
        loadInitialData()
    }
    
    /**
     * تحميل البيانات الأولية
     */
    private fun loadInitialData() {
        viewModelScope.launch {
            _isLoading.value = true
            
            try {
                // تحميل الأغاني
                loadSongs()
                
                // تحميل قوائم التشغيل
                loadPlaylists()
                
            } catch (e: Exception) {
                _message.value = "خطأ في تحميل البيانات: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * تحميل الأغاني
     */
    fun loadSongs() {
        viewModelScope.launch {
            _isLoading.value = true
            
            try {
                val songList = musicRepository.getAllSongs()
                _songs.value = songList
                
                // تحديث الألبومات والفنانين تلقائياً
                updateAlbumsFromSongs(songList)
                updateArtistsFromSongs(songList)
                
            } catch (e: Exception) {
                _message.value = "خطأ في تحميل الأغاني: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * تحميل الألبومات
     */
    fun loadAlbums() {
        viewModelScope.launch {
            _isLoading.value = true
            
            try {
                val albumList = musicRepository.getAllAlbums()
                _albums.value = albumList
                
            } catch (e: Exception) {
                _message.value = "خطأ في تحميل الألبومات: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * تحميل الفنانين
     */
    fun loadArtists() {
        viewModelScope.launch {
            _isLoading.value = true
            
            try {
                val artistList = musicRepository.getAllArtists()
                _artists.value = artistList
                
            } catch (e: Exception) {
                _message.value = "خطأ في تحميل الفنانين: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * تحميل قوائم التشغيل
     */
    fun loadPlaylists() {
        viewModelScope.launch {
            _isLoading.value = true
            
            try {
                val playlistList = playlistRepository.getAllPlaylists()
                _playlists.value = playlistList
                
            } catch (e: Exception) {
                _message.value = "خطأ في تحميل قوائم التشغيل: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * البحث
     */
    fun search(query: String) {
        viewModelScope.launch {
            _isLoading.value = true
            
            try {
                val results = musicRepository.search(query)
                _searchResults.value = results
                
            } catch (e: Exception) {
                _message.value = "خطأ في البحث: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * تشغيل أغنية
     */
    fun playSong(song: Song, playlist: List<Song>) {
        musicService?.playSong(song, playlist)
    }
    
    /**
     * تشغيل عشوائي لجميع الأغاني
     */
    fun shuffleAllSongs() {
        val currentSongs = _songs.value
        if (!currentSongs.isNullOrEmpty()) {
            val shuffledSongs = currentSongs.shuffled()
            musicService?.playSong(shuffledSongs.first(), shuffledSongs)
            musicService?.setShuffleEnabled(true)
            _message.value = "تم بدء التشغيل العشوائي"
        }
    }
    
    /**
     * تشغيل عشوائي لجميع الألبومات
     */
    fun shuffleAllAlbums() {
        viewModelScope.launch {
            try {
                val allSongs = musicRepository.getAllSongs()
                if (allSongs.isNotEmpty()) {
                    val shuffledSongs = allSongs.shuffled()
                    musicService?.playSong(shuffledSongs.first(), shuffledSongs)
                    musicService?.setShuffleEnabled(true)
                    _message.value = "تم بدء التشغيل العشوائي للألبومات"
                }
            } catch (e: Exception) {
                _message.value = "خطأ في التشغيل العشوائي: ${e.message}"
            }
        }
    }
    
    /**
     * تشغيل عشوائي لجميع الفنانين
     */
    fun shuffleAllArtists() {
        viewModelScope.launch {
            try {
                val allSongs = musicRepository.getAllSongs()
                if (allSongs.isNotEmpty()) {
                    val shuffledSongs = allSongs.shuffled()
                    musicService?.playSong(shuffledSongs.first(), shuffledSongs)
                    musicService?.setShuffleEnabled(true)
                    _message.value = "تم بدء التشغيل العشوائي للفنانين"
                }
            } catch (e: Exception) {
                _message.value = "خطأ في التشغيل العشوائي: ${e.message}"
            }
        }
    }
    
    /**
     * تشغيل عشوائي لجميع قوائم التشغيل
     */
    fun shuffleAllPlaylists() {
        viewModelScope.launch {
            try {
                val allPlaylists = playlistRepository.getAllPlaylists()
                if (allPlaylists.isNotEmpty()) {
                    val randomPlaylist = allPlaylists.random()
                    val playlistSongs = playlistRepository.getPlaylistSongs(randomPlaylist.id)
                    if (playlistSongs.isNotEmpty()) {
                        val shuffledSongs = playlistSongs.shuffled()
                        musicService?.playSong(shuffledSongs.first(), shuffledSongs)
                        musicService?.setShuffleEnabled(true)
                        _message.value = "تم بدء التشغيل العشوائي من ${randomPlaylist.name}"
                    }
                }
            } catch (e: Exception) {
                _message.value = "خطأ في التشغيل العشوائي: ${e.message}"
            }
        }
    }
    
    /**
     * فتح ألبوم
     */
    fun openAlbum(album: Album) {
        viewModelScope.launch {
            try {
                val albumSongs = musicRepository.getAlbumSongs(album.id)
                // يمكن إضافة navigation هنا للانتقال لشاشة الألبوم
                _message.value = "فتح ألبوم: ${album.getFormattedName()}"
            } catch (e: Exception) {
                _message.value = "خطأ في فتح الألبوم: ${e.message}"
            }
        }
    }
    
    /**
     * فتح فنان
     */
    fun openArtist(artist: Artist) {
        viewModelScope.launch {
            try {
                val artistSongs = musicRepository.getArtistSongs(artist.id)
                // يمكن إضافة navigation هنا للانتقال لشاشة الفنان
                _message.value = "فتح فنان: ${artist.getFormattedName()}"
            } catch (e: Exception) {
                _message.value = "خطأ في فتح الفنان: ${e.message}"
            }
        }
    }
    
    /**
     * فتح قائمة تشغيل
     */
    fun openPlaylist(playlist: Playlist) {
        viewModelScope.launch {
            try {
                val playlistSongs = playlistRepository.getPlaylistSongs(playlist.id)
                // يمكن إضافة navigation هنا للانتقال لشاشة قائمة التشغيل
                _message.value = "فتح قائمة التشغيل: ${playlist.name}"
            } catch (e: Exception) {
                _message.value = "خطأ في فتح قائمة التشغيل: ${e.message}"
            }
        }
    }
    
    /**
     * مسح الموسيقى
     */
    fun scanForMusic() {
        viewModelScope.launch {
            _isLoading.value = true
            _message.value = "جاري مسح الملفات الصوتية..."
            
            try {
                audioScanner.scanAudioFiles { progress ->
                    // تحديث التقدم
                }
                
                // إعادة تحميل البيانات
                loadSongs()
                _message.value = "تم الانتهاء من مسح الملفات الصوتية"
                
            } catch (e: Exception) {
                _message.value = "خطأ في مسح الملفات: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * تحديث الألبومات من الأغاني
     */
    private fun updateAlbumsFromSongs(songs: List<Song>) {
        val albumMap = mutableMapOf<Long, Album>()
        
        songs.forEach { song ->
            if (song.albumId != 0L && !albumMap.containsKey(song.albumId)) {
                albumMap[song.albumId] = Album(
                    id = song.albumId,
                    name = song.album,
                    artist = song.artist,
                    artistId = song.artistId,
                    songCount = songs.count { it.albumId == song.albumId },
                    year = song.year,
                    albumArtPath = song.albumArtPath,
                    firstSongPath = song.path
                )
            }
        }
        
        _albums.value = albumMap.values.toList()
    }
    
    /**
     * تحديث الفنانين من الأغاني
     */
    private fun updateArtistsFromSongs(songs: List<Song>) {
        val artistMap = mutableMapOf<Long, Artist>()
        
        songs.forEach { song ->
            if (song.artistId != 0L && !artistMap.containsKey(song.artistId)) {
                val artistSongs = songs.filter { it.artistId == song.artistId }
                val albumCount = artistSongs.map { it.albumId }.distinct().size
                
                artistMap[song.artistId] = Artist(
                    id = song.artistId,
                    name = song.artist,
                    albumCount = albumCount,
                    songCount = artistSongs.size,
                    duration = artistSongs.sumOf { it.duration },
                    albumArtPath = song.albumArtPath
                )
            }
        }
        
        _artists.value = artistMap.values.toList()
    }
    
    /**
     * ربط خدمة الموسيقى
     */
    fun bindMusicService(service: MusicService) {
        musicService = service
    }
    
    /**
     * إلغاء ربط خدمة الموسيقى
     */
    fun unbindMusicService() {
        musicService = null
    }
}

/**
 * نموذج نتائج البحث
 */
data class SearchResults(
    val songs: List<Song> = emptyList(),
    val albums: List<Album> = emptyList(),
    val artists: List<Artist> = emptyList(),
    val playlists: List<Playlist> = emptyList()
)
