package com.musicplayer.pro.models

/**
 * نموذج بيانات نطاق المعادل الصوتي
 */
data class EqualizerBand(
    val bandIndex: Short,
    val frequency: Int,
    val level: Short,
    val minLevel: Short = -1500,
    val maxLevel: Short = 1500
) {
    
    /**
     * الحصول على التردد بتنسيق قابل للقراءة
     */
    fun getFormattedFrequency(): String {
        return when {
            frequency < 1000 -> "${frequency} Hz"
            frequency < 1000000 -> "${frequency / 1000} kHz"
            else -> "${frequency / 1000000} MHz"
        }
    }
    
    /**
     * الحصول على المستوى بالديسيبل
     */
    fun getLevelInDb(): Float {
        return level / 100.0f
    }
    
    /**
     * تحويل المستوى إلى نسبة مئوية (0-100)
     */
    fun getLevelAsPercentage(): Int {
        val range = maxLevel - minLevel
        return ((level - minLevel) * 100 / range).toInt()
    }
    
    /**
     * تحويل النسبة المئوية إلى مستوى
     */
    fun percentageToLevel(percentage: Int): Short {
        val range = maxLevel - minLevel
        return (minLevel + (percentage * range / 100)).toShort()
    }
}
