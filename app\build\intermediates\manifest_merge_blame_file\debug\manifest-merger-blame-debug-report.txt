1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.musicplayer.pro"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <!-- الأذونات الأساسية -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:6:5-67
12-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:7:5-79
13-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
14-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:8:5-76
14-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:8:22-73
15
16    <!-- أذونات التخزين -->
17    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
17-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:11:5-80
17-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:11:22-77
18    <uses-permission
18-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:12:5-13:38
19        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
19-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:12:22-78
20        android:maxSdkVersion="28" />
20-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:13:9-35
21    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
21-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:14:5-15:40
21-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:14:22-79
22    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
22-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:16:5-75
22-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:16:22-72
23
24    <!-- أذونات الصوت -->
25    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
25-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:19:5-80
25-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:19:22-77
26    <uses-permission android:name="android.permission.RECORD_AUDIO" />
26-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:20:5-71
26-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:20:22-68
27
28    <!-- أذونات الخدمات -->
29    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
29-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:23:5-77
29-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:23:22-74
30    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
30-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:24:5-92
30-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:24:22-89
31    <uses-permission android:name="android.permission.WAKE_LOCK" />
31-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:25:5-68
31-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:25:22-65
32    <uses-permission android:name="android.permission.VIBRATE" />
32-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:26:5-66
32-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:26:22-63
33
34    <!-- أذونات البلوتوث -->
35    <uses-permission android:name="android.permission.BLUETOOTH" />
35-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:29:5-68
35-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:29:22-65
36    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
36-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:30:5-74
36-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:30:22-71
37    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
37-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:31:5-76
37-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:31:22-73
38    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
38-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:32:5-73
38-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:32:22-70
39
40    <!-- أذونات الإشعارات -->
41    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
41-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:35:5-77
41-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:35:22-74
42    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" />
42-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:36:5-85
42-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:36:22-82
43    <uses-permission android:name="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" />
43-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:37:5-93
43-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:37:22-90
44
45    <!-- أذونات إضافية (اختيارية) -->
46    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
46-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:40:5-81
46-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:40:22-78
47    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
47-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:41:5-75
47-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:41:22-72
48    <uses-permission android:name="android.permission.MEDIA_CONTENT_CONTROL" />
48-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:42:5-80
48-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:42:22-77
49
50    <!-- دعم الميزات -->
51    <uses-feature
51-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:45:5-47:35
52        android:name="android.hardware.audio.output"
52-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:46:9-53
53        android:required="true" />
53-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:47:9-32
54    <uses-feature
54-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:48:5-50:36
55        android:name="android.hardware.microphone"
55-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:49:9-51
56        android:required="false" />
56-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:50:9-33
57    <uses-feature
57-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:51:5-53:36
58        android:name="android.hardware.bluetooth"
58-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:52:9-50
59        android:required="false" />
59-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:53:9-33
60    <uses-feature
60-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:54:5-56:36
61        android:name="android.hardware.wifi"
61-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:55:9-45
62        android:required="false" />
62-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:56:9-33
63
64    <!-- دعم Android Auto -->
65    <uses-feature
65-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:214:5-216:36
66        android:name="android.hardware.type.automotive"
66-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:215:9-56
67        android:required="false" />
67-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:216:9-33
68
69    <!-- دعم Android TV -->
70    <uses-feature
70-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:219:5-221:36
71        android:name="android.software.leanback"
71-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:220:9-49
72        android:required="false" />
72-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:221:9-33
73
74    <!-- دعم Wear OS -->
75    <uses-feature
75-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:224:5-226:36
76        android:name="android.hardware.type.watch"
76-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:225:9-51
77        android:required="false" />
77-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:226:9-33
78
79    <permission
79-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c108dd56627f30fe94755d1a2faeaf2\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
80        android:name="com.musicplayer.pro.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
80-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c108dd56627f30fe94755d1a2faeaf2\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
81        android:protectionLevel="signature" />
81-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c108dd56627f30fe94755d1a2faeaf2\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
82
83    <uses-permission android:name="com.musicplayer.pro.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
83-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c108dd56627f30fe94755d1a2faeaf2\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
83-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c108dd56627f30fe94755d1a2faeaf2\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
84
85    <application
85-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:58:5-211:19
86        android:name="com.musicplayer.pro.MusicPlayerApplication"
86-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:59:9-47
87        android:allowBackup="true"
87-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:60:9-35
88        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
88-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c108dd56627f30fe94755d1a2faeaf2\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
89        android:dataExtractionRules="@xml/data_extraction_rules"
89-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:61:9-65
90        android:debuggable="true"
91        android:extractNativeLibs="true"
92        android:fullBackupContent="@xml/backup_rules"
92-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:62:9-54
93        android:icon="@mipmap/ic_launcher"
93-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:63:9-43
94        android:label="@string/app_name"
94-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:64:9-41
95        android:preserveLegacyExternalStorage="true"
95-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:69:9-53
96        android:requestLegacyExternalStorage="true"
96-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:68:9-52
97        android:roundIcon="@mipmap/ic_launcher_round"
97-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:65:9-54
98        android:supportsRtl="true"
98-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:66:9-35
99        android:theme="@style/Theme.MusicPlayerPro" >
99-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:67:9-52
100
101        <!-- النشاط الرئيسي -->
102        <activity
102-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:73:9-104:20
103            android:name="com.musicplayer.pro.MainActivity"
103-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:74:13-41
104            android:exported="true"
104-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:75:13-36
105            android:launchMode="singleTop"
105-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:76:13-43
106            android:screenOrientation="portrait"
106-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:77:13-49
107            android:theme="@style/Theme.MusicPlayerPro" >
107-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:78:13-56
108            <intent-filter>
108-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:79:13-82:29
109                <action android:name="android.intent.action.MAIN" />
109-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:80:17-69
109-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:80:25-66
110
111                <category android:name="android.intent.category.LAUNCHER" />
111-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:81:17-77
111-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:81:27-74
112            </intent-filter>
113
114            <!-- دعم ملفات الصوت -->
115            <intent-filter>
115-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:85:13-90:29
116                <action android:name="android.intent.action.VIEW" />
116-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:86:17-69
116-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:86:25-66
117
118                <category android:name="android.intent.category.DEFAULT" />
118-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:87:17-76
118-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:87:27-73
119                <category android:name="android.intent.category.BROWSABLE" />
119-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:88:17-78
119-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:88:27-75
120
121                <data android:mimeType="audio/*" />
121-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:89:17-52
121-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:89:23-49
122            </intent-filter>
123
124            <!-- دعم الروابط -->
125            <intent-filter android:autoVerify="true" >
125-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:93:13-103:29
125-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:93:28-53
126                <action android:name="android.intent.action.VIEW" />
126-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:86:17-69
126-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:86:25-66
127
128                <category android:name="android.intent.category.DEFAULT" />
128-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:87:17-76
128-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:87:27-73
129                <category android:name="android.intent.category.BROWSABLE" />
129-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:88:17-78
129-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:88:27-75
130
131                <data
131-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:89:17-52
132                    android:host="youtube.com"
132-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:97:45-71
133                    android:scheme="http" />
133-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:97:23-44
134                <data
134-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:89:17-52
135                    android:host="youtube.com"
135-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:97:45-71
136                    android:scheme="https" />
136-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:97:23-44
137                <data
137-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:89:17-52
138                    android:host="www.youtube.com"
138-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:97:45-71
139                    android:scheme="http" />
139-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:97:23-44
140                <data
140-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:89:17-52
141                    android:host="www.youtube.com"
141-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:97:45-71
142                    android:scheme="https" />
142-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:97:23-44
143                <data
143-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:89:17-52
144                    android:host="youtu.be"
144-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:97:45-71
145                    android:scheme="http" />
145-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:97:23-44
146                <data
146-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:89:17-52
147                    android:host="youtu.be"
147-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:97:45-71
148                    android:scheme="https" />
148-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:97:23-44
149            </intent-filter>
150        </activity>
151
152        <!-- نشاط الإعدادات -->
153        <activity
153-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:107:9-115:20
154            android:name="com.musicplayer.pro.SettingsActivity"
154-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:108:13-45
155            android:exported="false"
155-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:109:13-37
156            android:parentActivityName="com.musicplayer.pro.MainActivity"
156-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:111:13-55
157            android:theme="@style/Theme.MusicPlayerPro" >
157-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:110:13-56
158            <meta-data
158-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:112:13-114:49
159                android:name="android.support.PARENT_ACTIVITY"
159-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:113:17-63
160                android:value=".MainActivity" />
160-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:114:17-46
161        </activity>
162
163        <!-- نشاط المعادل الصوتي -->
164        <activity
164-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:118:9-126:20
165            android:name="com.musicplayer.pro.EqualizerActivity"
165-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:119:13-46
166            android:exported="false"
166-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:120:13-37
167            android:parentActivityName="com.musicplayer.pro.SettingsActivity"
167-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:122:13-59
168            android:theme="@style/Theme.MusicPlayerPro" >
168-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:121:13-56
169            <meta-data
169-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:112:13-114:49
170                android:name="android.support.PARENT_ACTIVITY"
170-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:113:17-63
171                android:value=".SettingsActivity" />
171-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:114:17-46
172        </activity>
173
174        <!-- خدمة الموسيقى -->
175        <service
175-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:129:9-137:19
176            android:name="com.musicplayer.pro.services.MusicService"
176-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:130:13-50
177            android:enabled="true"
177-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:131:13-35
178            android:exported="false"
178-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:132:13-37
179            android:foregroundServiceType="mediaPlayback" >
179-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:133:13-58
180            <intent-filter>
180-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:134:13-136:29
181                <action android:name="android.media.browse.MediaBrowserService" />
181-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:135:17-83
181-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:135:25-80
182            </intent-filter>
183        </service>
184
185        <!-- خدمة التحميل -->
186        <service
186-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:140:9-144:56
187            android:name="com.musicplayer.pro.services.DownloadService"
187-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:141:13-53
188            android:enabled="true"
188-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:142:13-35
189            android:exported="false"
189-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:143:13-37
190            android:foregroundServiceType="dataSync" />
190-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:144:13-53
191
192        <!-- مستقبل البث للتحكم في الوسائط -->
193        <receiver
193-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:147:9-154:20
194            android:name="com.musicplayer.pro.receivers.MediaButtonReceiver"
194-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:148:13-58
195            android:enabled="true"
195-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:149:13-35
196            android:exported="true" >
196-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:150:13-36
197            <intent-filter android:priority="1000" >
197-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:151:13-153:29
197-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:151:28-51
198                <action android:name="android.intent.action.MEDIA_BUTTON" />
198-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:152:17-77
198-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:152:25-74
199            </intent-filter>
200        </receiver>
201
202        <!-- مستقبل البث لبدء التشغيل -->
203        <receiver
203-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:157:9-167:20
204            android:name="com.musicplayer.pro.receivers.BootReceiver"
204-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:158:13-51
205            android:enabled="true"
205-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:159:13-35
206            android:exported="true" >
206-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:160:13-36
207            <intent-filter android:priority="1000" >
207-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:161:13-166:29
207-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:161:28-51
208                <action android:name="android.intent.action.BOOT_COMPLETED" />
208-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:162:17-79
208-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:162:25-76
209                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
209-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:163:17-84
209-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:163:25-81
210                <action android:name="android.intent.action.PACKAGE_REPLACED" />
210-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:164:17-81
210-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:164:25-78
211
212                <data android:scheme="package" />
212-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:89:17-52
212-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:97:23-44
213            </intent-filter>
214        </receiver>
215
216        <!-- مستقبل البث للسماعات -->
217        <receiver
217-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:170:9-178:20
218            android:name="com.musicplayer.pro.receivers.HeadsetReceiver"
218-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:171:13-54
219            android:enabled="true"
219-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:172:13-35
220            android:exported="true" >
220-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:173:13-36
221            <intent-filter>
221-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:174:13-177:29
222                <action android:name="android.intent.action.HEADSET_PLUG" />
222-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:175:17-77
222-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:175:25-74
223                <action android:name="android.bluetooth.a2dp.profile.action.CONNECTION_STATE_CHANGED" />
223-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:176:17-105
223-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:176:25-102
224            </intent-filter>
225        </receiver>
226
227        <!-- مزود المحتوى للملفات -->
228        <provider
229            android:name="androidx.core.content.FileProvider"
229-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:182:13-62
230            android:authorities="com.musicplayer.pro.fileprovider"
230-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:183:13-64
231            android:exported="false"
231-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:184:13-37
232            android:grantUriPermissions="true" >
232-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:185:13-47
233            <meta-data
233-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:186:13-188:54
234                android:name="android.support.FILE_PROVIDER_PATHS"
234-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:187:17-67
235                android:resource="@xml/file_paths" />
235-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:188:17-51
236        </provider>
237
238        <!-- MediaSession -->
239        <meta-data
239-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:192:9-194:36
240            android:name="android.media.session.MediaSession"
240-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:193:13-62
241            android:value="true" />
241-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:194:13-33
242
243        <!-- دعم Auto -->
244        <meta-data
244-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:197:9-199:59
245            android:name="com.google.android.gms.car.application"
245-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:198:13-66
246            android:resource="@xml/automotive_app_desc" />
246-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:199:13-56
247
248        <!-- دعم Wear OS -->
249        <meta-data
249-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:202:9-204:37
250            android:name="com.google.android.wearable.standalone"
250-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:203:13-66
251            android:value="false" />
251-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:204:13-34
252
253        <!-- إعدادات الشبكة -->
254        <meta-data
254-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:207:9-209:36
255            android:name="android.webkit.WebView.MetricsOptOut"
255-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:208:13-64
256            android:value="true" />
256-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:209:13-33
257
258        <activity
258-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b7043eedc50cada365136aa0e9ac9a0\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:27:9-29:72
259            android:name="com.karumi.dexter.DexterActivity"
259-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b7043eedc50cada365136aa0e9ac9a0\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:28:13-60
260            android:theme="@style/Dexter.Internal.Theme.Transparent" />
260-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b7043eedc50cada365136aa0e9ac9a0\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:29:13-69
261
262        <provider
262-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7eec3ffd9134b39a33221bcb756c708\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
263            android:name="androidx.startup.InitializationProvider"
263-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7eec3ffd9134b39a33221bcb756c708\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
264            android:authorities="com.musicplayer.pro.androidx-startup"
264-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7eec3ffd9134b39a33221bcb756c708\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
265            android:exported="false" >
265-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7eec3ffd9134b39a33221bcb756c708\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
266            <meta-data
266-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7eec3ffd9134b39a33221bcb756c708\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
267                android:name="androidx.emoji2.text.EmojiCompatInitializer"
267-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7eec3ffd9134b39a33221bcb756c708\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
268                android:value="androidx.startup" />
268-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7eec3ffd9134b39a33221bcb756c708\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
269            <meta-data
269-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47b54bf287343bbce7eafca9f7c47b52\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
270                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
270-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47b54bf287343bbce7eafca9f7c47b52\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
271                android:value="androidx.startup" />
271-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47b54bf287343bbce7eafca9f7c47b52\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
272            <meta-data
272-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
273                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
273-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
274                android:value="androidx.startup" />
274-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
275        </provider>
276
277        <uses-library
277-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6b7dbe88d3a04c6d55aa7c581f20597\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
278            android:name="androidx.window.extensions"
278-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6b7dbe88d3a04c6d55aa7c581f20597\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
279            android:required="false" />
279-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6b7dbe88d3a04c6d55aa7c581f20597\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
280        <uses-library
280-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6b7dbe88d3a04c6d55aa7c581f20597\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
281            android:name="androidx.window.sidecar"
281-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6b7dbe88d3a04c6d55aa7c581f20597\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
282            android:required="false" />
282-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6b7dbe88d3a04c6d55aa7c581f20597\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
283
284        <receiver
284-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
285            android:name="androidx.profileinstaller.ProfileInstallReceiver"
285-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
286            android:directBootAware="false"
286-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
287            android:enabled="true"
287-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
288            android:exported="true"
288-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
289            android:permission="android.permission.DUMP" >
289-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
290            <intent-filter>
290-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
291                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
291-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
291-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
292            </intent-filter>
293            <intent-filter>
293-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
294                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
294-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
294-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
295            </intent-filter>
296            <intent-filter>
296-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
297                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
297-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
297-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
298            </intent-filter>
299            <intent-filter>
299-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
300                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
300-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
300-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
301            </intent-filter>
302        </receiver>
303    </application>
304
305</manifest>
