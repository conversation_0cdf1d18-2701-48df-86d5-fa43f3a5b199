package com.musicplayer.pro.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.media.MediaMetadataRetriever
import android.widget.ImageView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import com.musicplayer.pro.R
import com.musicplayer.pro.models.Song

/**
 * محمل الصور المحسن
 */
object ImageLoader {

    /**
     * تحميل صورة غلاف الألبوم (للتوافق مع الكود القديم)
     */
    fun loadAlbumArt(imageView: ImageView, albumArt: String?) {
        if (albumArt.isNullOrEmpty()) {
            imageView.setImageResource(R.drawable.default_album_cover)
        } else {
            imageView.setImageResource(R.drawable.default_album_cover)
        }
    }

    /**
     * تحميل صورة الأغنية في ImageView
     */
    fun loadSongImage(
        context: Context,
        song: Song,
        imageView: ImageView,
        placeholder: Int = R.drawable.default_album_cover
    ) {
        val requestOptions = RequestOptions()
            .placeholder(placeholder)
            .error(placeholder)
            .diskCacheStrategy(DiskCacheStrategy.ALL)
            .centerCrop()

        // محاولة تحميل صورة الألبوم من الملف
        val albumArt = extractAlbumArt(song.path)

        if (albumArt != null) {
            Glide.with(context)
                .load(albumArt)
                .apply(requestOptions)
                .into(imageView)
        } else {
            // استخدام الصورة الافتراضية
            Glide.with(context)
                .load(placeholder)
                .apply(requestOptions)
                .into(imageView)
        }
    }

    /**
     * استخراج صورة الألبوم من ملف الأغنية
     */
    private fun extractAlbumArt(songPath: String): Bitmap? {
        return try {
            val retriever = MediaMetadataRetriever()
            retriever.setDataSource(songPath)
            val art = retriever.embeddedPicture
            retriever.release()

            if (art != null) {
                BitmapFactory.decodeByteArray(art, 0, art.size)
            } else {
                null
            }
        } catch (e: Exception) {
            null
        }
    }

    /**
     * تحميل صورة دائرية للأغنية
     */
    fun loadCircularSongImage(
        context: Context,
        song: Song,
        imageView: ImageView,
        placeholder: Int = R.drawable.default_album_cover
    ) {
        val requestOptions = RequestOptions()
            .placeholder(placeholder)
            .error(placeholder)
            .diskCacheStrategy(DiskCacheStrategy.ALL)
            .circleCrop()

        val albumArt = extractAlbumArt(song.path)

        if (albumArt != null) {
            Glide.with(context)
                .load(albumArt)
                .apply(requestOptions)
                .into(imageView)
        } else {
            Glide.with(context)
                .load(placeholder)
                .apply(requestOptions)
                .into(imageView)
        }
    }

    /**
     * تحميل صورة مع تأثير ضبابي للخلفية
     */
    fun loadBlurredSongImage(
        context: Context,
        song: Song,
        imageView: ImageView,
        blurRadius: Int = 25,
        placeholder: Int = R.drawable.default_album_cover
    ) {
        val albumArt = extractAlbumArt(song.path)

        if (albumArt != null) {
            Glide.with(context)
                .load(albumArt)
                .apply(RequestOptions.bitmapTransform(
                    jp.wasabeef.glide.transformations.BlurTransformation(blurRadius)
                ))
                .placeholder(placeholder)
                .error(placeholder)
                .into(imageView)
        } else {
            imageView.setImageResource(placeholder)
        }
    }
}
