#Sun Jul 13 15:24:24 AST 2025
path.4=classes2.dex
path.3=8/classes.dex
path.2=10/classes.dex
path.1=0/classes.dex
renamed.8=classes9.dex
path.8=7/classes.dex
path.7=4/classes.dex
path.6=2/classes.dex
path.5=15/classes.dex
path.0=classes.dex
base.4=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes2.dex
base.3=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\8\\classes.dex
base.2=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\10\\classes.dex
base.1=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\0\\classes.dex
base.0=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes.dex
renamed.3=classes4.dex
renamed.2=classes3.dex
renamed.1=classes2.dex
renamed.0=classes.dex
renamed.7=classes8.dex
base.8=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\7\\classes.dex
renamed.6=classes7.dex
base.7=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\4\\classes.dex
renamed.5=classes6.dex
base.6=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\2\\classes.dex
renamed.4=classes5.dex
base.5=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\15\\classes.dex
