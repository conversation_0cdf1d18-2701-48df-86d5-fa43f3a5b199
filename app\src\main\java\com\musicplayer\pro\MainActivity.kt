package com.musicplayer.pro

import android.Manifest
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.content.pm.PackageManager
import android.os.Bundle
import android.os.IBinder
import android.util.Log
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.musicplayer.pro.fragments.*
import com.musicplayer.pro.services.MusicService
import com.musicplayer.pro.utils.MediaScanner
import com.musicplayer.pro.utils.PermissionManager
import com.musicplayer.pro.utils.ThemeManager
import kotlinx.coroutines.launch

/**
 * النشاط الرئيسي لتطبيق الموسيقى
 * يحتوي على جميع الميزات من النسخة Python
 */
class MainActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "MainActivity"
        private const val REQUEST_PERMISSIONS = 1001
    }

    // UI Components
    private lateinit var viewPager: ViewPager2
    private lateinit var tabLayout: TabLayout
    private lateinit var bottomMusicBar: View

    // Progress Update Handler
    private val progressHandler = android.os.Handler(android.os.Looper.getMainLooper())
    private var progressUpdateRunnable: Runnable? = null

    // Managers
    private lateinit var permissionManager: PermissionManager
    private lateinit var themeManager: ThemeManager
    private lateinit var mediaScanner: MediaScanner

    // Music Service
    private var musicService: MusicService? = null
    private var isServiceBound = false

    // Service Connection
    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            val binder = service as MusicService.MusicBinder
            musicService = binder.getService()
            isServiceBound = true

            // إعداد callbacks للخدمة
            setupServiceCallbacks()
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            musicService = null
            isServiceBound = false
        }
    }

    // Permission Launcher
    private val permissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val grantedPermissions = permissions.filterValues { it }.keys
        val deniedPermissions = permissions.filterValues { !it }.keys

        // فحص الأذونات الأساسية المطلوبة
        val essentialPermissions = mutableListOf<String>().apply {
            // إذن قراءة الملفات (حسب إصدار الأندرويد)
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
                add(Manifest.permission.READ_MEDIA_AUDIO)
            } else {
                add(Manifest.permission.READ_EXTERNAL_STORAGE)
            }
            // أذونات الشبكة
            add(Manifest.permission.INTERNET)
            add(Manifest.permission.ACCESS_NETWORK_STATE)
        }

        val essentialGranted = essentialPermissions.all { permission ->
            ContextCompat.checkSelfPermission(this, permission) == PackageManager.PERMISSION_GRANTED
        }

        if (essentialGranted) {
            // الأذونات الأساسية متوفرة، يمكن تشغيل التطبيق
            showPermissionResults(grantedPermissions, deniedPermissions)
            initializeApp()
        } else {
            // الأذونات الأساسية مفقودة
            showCriticalPermissionDeniedMessage()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // تهيئة المدراء
        initializeManagers()

        // تطبيق الثيم
        themeManager.applyTheme()

        setContentView(R.layout.activity_main)

        // تهيئة UI
        initializeUI()

        // طلب الأذونات
        requestNecessaryPermissions()
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.main_menu, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_scan_music -> {
                startAutoMusicScan()
                true
            }
            R.id.action_settings -> {
                openSettings()
                true
            }
            R.id.action_about -> {
                showAboutDialog()
                true
            }
            R.id.action_exit -> {
                finishAndRemoveTask()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    /**
     * تهيئة المدراء المختلفة
     */
    private fun initializeManagers() {
        permissionManager = PermissionManager(this)
        themeManager = ThemeManager(this)
    }

    /**
     * تهيئة واجهة المستخدم
     */
    private fun initializeUI() {
        viewPager = findViewById(R.id.viewPager)
        tabLayout = findViewById(R.id.tabLayout)
        bottomMusicBar = findViewById(R.id.bottomMusicBar)

        setupViewPager()
        setupBottomMusicBar()
    }

    /**
     * إعداد ViewPager مع الشاشات المختلفة
     */
    private fun setupViewPager() {
        val adapter = MainPagerAdapter(this)
        viewPager.adapter = adapter

        // ربط TabLayout مع ViewPager
        TabLayoutMediator(tabLayout, viewPager) { tab, position ->
            tab.text = when (position) {
                0 -> getString(R.string.main_screen)
                1 -> getString(R.string.now_playing)
                2 -> getString(R.string.downloads)
                else -> "Tab $position"
            }

            // إضافة أيقونات للتبويبات
            tab.setIcon(when (position) {
                0 -> R.drawable.ic_music_library
                1 -> R.drawable.ic_now_playing
                2 -> R.drawable.ic_download
                else -> R.drawable.ic_music_library
            })
        }.attach()

        // إضافة مراقب لتغيير الصفحات
        viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                handlePageChange(position)
            }
        })
    }

    /**
     * معالجة تغيير الصفحة
     */
    private fun handlePageChange(position: Int) {
        when (position) {
            0 -> {
                // الصفحة الرئيسية - إظهار الشريط السفلي إذا كان هناك تشغيل
                showBottomMusicBarIfPlaying()
            }
            1 -> {
                // شاشة التشغيل الحالي - إخفاء الشريط السفلي
                hideBottomMusicBar()
            }
            2 -> {
                // صفحة التحميلات - إظهار الشريط السفلي إذا كان هناك تشغيل
                showBottomMusicBarIfPlaying()
            }
        }
    }

    /**
     * إعداد الشريط السفلي للموسيقى
     */
    private fun setupBottomMusicBar() {
        // إخفاء الشريط في البداية
        bottomMusicBar.visibility = View.GONE

        // إعداد النقر للانتقال لشاشة التشغيل
        bottomMusicBar.setOnClickListener {
            viewPager.currentItem = 1 // الانتقال لتبويب "قيد التشغيل"
        }

        // إعداد أزرار التحكم
        setupBottomMusicBarControls()
    }

    /**
     * إعداد زر التحكم في الشريط السفلي
     */
    private fun setupBottomMusicBarControls() {
        try {
            val playPauseButton = bottomMusicBar.findViewById<View>(R.id.btnMiniPlayPause)

            // زر التشغيل/الإيقاف فقط
            playPauseButton?.setOnClickListener {
                musicService?.let { service ->
                    service.togglePlayPause()
                    animatePlayButton(playPauseButton as? android.widget.ImageButton)

                    // تحديث فوري لحالة التشغيل
                    playPauseButton.postDelayed({
                        updatePlaybackUI(service.isPlaying())
                    }, 100)
                }
            }

        } catch (e: Exception) {
            Log.e("MainActivity", "خطأ في إعداد زر التحكم: ${e.message}")
        }
    }

    /**
     * طلب الأذونات الضرورية فقط
     */
    private fun requestNecessaryPermissions() {
        val permissions = mutableListOf<String>().apply {
            // أذونات التخزين (أساسية لقراءة ملفات الموسيقى)
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
                add(Manifest.permission.READ_MEDIA_AUDIO)
            } else {
                add(Manifest.permission.READ_EXTERNAL_STORAGE)
            }

            // أذونات الشبكة (أساسية للتحميل)
            add(Manifest.permission.INTERNET)
            add(Manifest.permission.ACCESS_NETWORK_STATE)

            // أذونات الإشعارات (أساسية لأدوات التحكم)
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
                add(Manifest.permission.POST_NOTIFICATIONS)
            }

            // أذونات الصوت (للمعادل الصوتي)
            add(Manifest.permission.MODIFY_AUDIO_SETTINGS)
        }

        val permissionsToRequest = permissions.filter {
            ContextCompat.checkSelfPermission(this, it) != PackageManager.PERMISSION_GRANTED
        }

        if (permissionsToRequest.isNotEmpty()) {
            showPermissionExplanationDialog(permissionsToRequest.toTypedArray())
        } else {
            initializeApp()
        }
    }

    /**
     * تهيئة التطبيق بعد الحصول على الأذونات
     */
    private fun initializeApp() {
        // تهيئة فاحص الوسائط
        mediaScanner = MediaScanner(this)

        // بدء خدمة الموسيقى
        startMusicService()

        // تحميل البيانات
        loadInitialData()

        // بدء البحث التلقائي عن الأغاني
        startAutoMusicScan()
    }

    /**
     * بدء خدمة الموسيقى
     */
    private fun startMusicService() {
        val intent = Intent(this, MusicService::class.java)
        startService(intent)
        bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE)
    }

    /**
     * إعداد callbacks للخدمة
     */
    private fun setupServiceCallbacks() {
        musicService?.let { service ->
            // ربط MusicService مع ViewModel
            bindMusicServiceToViewModel(service)

            service.setOnPlaybackStateChangedListener { isPlaying ->
                // تحديث UI عند تغيير حالة التشغيل
                updatePlaybackUI(isPlaying)
            }

            service.setOnSongChangedListener { song ->
                // تحديث UI عند تغيير الأغنية
                Log.d("MainActivity", "تغيرت الأغنية إلى: ${song.title}")
                updateCurrentSongUI(song)
                // إظهار الشريط السفلي (إلا إذا كنا في شاشة التشغيل الحالي)
                if (viewPager.currentItem != 1) {
                    showBottomMusicBar(song)
                }
                // تحديث حالة التشغيل
                updatePlaybackUI(service.isPlaying())
            }
        }
    }

    /**
     * ربط MusicService مع جميع ViewModels
     */
    private fun bindMusicServiceToViewModel(service: MusicService) {
        // يمكن الوصول للـ ViewModels من الـ Fragments
        // أو إنشاء ViewModel مشترك في MainActivity
    }

    /**
     * تحديث UI عند تغيير حالة التشغيل
     */
    private fun updatePlaybackUI(isPlaying: Boolean) {
        runOnUiThread {
            try {
                // تحديث الشريط السفلي
                updateBottomBarPlaybackState(isPlaying)

                // تحديث أيقونة زر التشغيل في الشريط السفلي
                val playPauseButton = bottomMusicBar.findViewById<android.widget.ImageButton>(R.id.btnMiniPlayPause)
                playPauseButton?.let {
                    val iconRes = if (isPlaying) R.drawable.ic_pause else R.drawable.ic_play
                    it.setImageResource(iconRes)
                }

                Log.d("MainActivity", "تم تحديث حالة التشغيل: $isPlaying")
            } catch (e: Exception) {
                Log.e("MainActivity", "خطأ في تحديث UI للتشغيل: ${e.message}")
            }
        }
    }

    /**
     * تحديث UI عند تغيير الأغنية
     */
    private fun updateCurrentSongUI(song: com.musicplayer.pro.models.Song) {
        runOnUiThread {
            try {
                // تحديث الشريط السفلي بالأغنية الجديدة
                updateBottomBarSongInfo(song)

                // إظهار الشريط السفلي إذا لم يكن ظاهراً (إلا في شاشة التشغيل الحالي)
                if (bottomMusicBar.visibility != View.VISIBLE && viewPager.currentItem != 1) {
                    showBottomMusicBar(song)
                }

                Log.d("MainActivity", "تم تحديث الأغنية: ${song.title}")
            } catch (e: Exception) {
                Log.e("MainActivity", "خطأ في تحديث UI للأغنية: ${e.message}")
            }
        }
    }

    /**
     * تحميل البيانات الأولية
     */
    private fun loadInitialData() {
        // تحميل قوائم التشغيل والأغاني المحفوظة
    }

    /**
     * بدء البحث التلقائي عن الأغاني
     */
    private fun startAutoMusicScan() {
        if (!::mediaScanner.isInitialized) {
            Toast.makeText(this, "فاحص الوسائط غير جاهز", Toast.LENGTH_SHORT).show()
            return
        }

        lifecycleScope.launch {
            try {
                // عرض رسالة البحث
                Toast.makeText(this@MainActivity, "🔍 جاري البحث عن الأغاني...", Toast.LENGTH_SHORT).show()

                // فحص سريع للحصول على العدد
                val songCount = mediaScanner.getQuickSongCount()

                if (songCount > 0) {
                    // بدء الفحص الشامل
                    val songs = mediaScanner.scanForMusic()

                    // عرض النتائج
                    showScanResults(songs.size)

                    // إرسال النتائج للـ fragments
                    broadcastScanResults(songs)
                } else {
                    Toast.makeText(this@MainActivity, "لم يتم العثور على أغاني في الجهاز", Toast.LENGTH_LONG).show()
                }

            } catch (e: Exception) {
                Toast.makeText(this@MainActivity, "خطأ في البحث عن الأغاني: ${e.message}", Toast.LENGTH_LONG).show()
            }
        }
    }

    /**
     * عرض نتائج الفحص
     */
    private fun showScanResults(songCount: Int) {
        val message = when {
            songCount == 0 -> "لم يتم العثور على أغاني"
            songCount == 1 -> "تم العثور على أغنية واحدة"
            songCount <= 10 -> "تم العثور على $songCount أغاني"
            else -> "تم العثور على $songCount أغنية 🎵"
        }

        Toast.makeText(this, message, Toast.LENGTH_LONG).show()
    }

    /**
     * إرسال نتائج الفحص للـ fragments
     */
    private fun broadcastScanResults(songs: List<com.musicplayer.pro.models.Song>) {
        // إرسال broadcast للـ fragments
        val intent = Intent("com.musicplayer.pro.SONGS_SCANNED")
        intent.putExtra("song_count", songs.size)

        // تحويل الأغاني إلى ArrayList للإرسال
        val songsList = ArrayList(songs)
        intent.putParcelableArrayListExtra("songs_list", songsList)

        sendBroadcast(intent)

        // تحديث ViewModel مباشرة
        updateViewModel(songs)
    }

    /**
     * تحديث ViewModel بالأغاني
     */
    private fun updateViewModel(songs: List<com.musicplayer.pro.models.Song>) {
        try {
            // يمكن إضافة تحديث ViewModel هنا لاحقاً
            // أو حفظ الأغاني في قاعدة البيانات
        } catch (e: Exception) {
            Log.e("MainActivity", "خطأ في تحديث ViewModel: ${e.message}")
        }
    }

    /**
     * إظهار الشريط السفلي عند تشغيل أغنية
     */
    fun showBottomMusicBar(song: com.musicplayer.pro.models.Song) {
        try {
            // إظهار الشريط مع رسوم متحركة
            if (bottomMusicBar.visibility != View.VISIBLE) {
                bottomMusicBar.visibility = View.VISIBLE
                bottomMusicBar.translationY = bottomMusicBar.height.toFloat()
                bottomMusicBar.animate()
                    .translationY(0f)
                    .setDuration(300)
                    .start()
            }

            // تحديث معلومات الأغنية
            updateBottomBarSongInfo(song)

            // تحديث حالة التشغيل
            updateBottomBarPlaybackState(true)

            // بدء تحديث شريط التقدم
            startProgressUpdates()

        } catch (e: Exception) {
            Log.e("MainActivity", "خطأ في إظهار الشريط السفلي: ${e.message}")
        }
    }

    /**
     * تحديث معلومات الأغنية في الشريط السفلي
     */
    private fun updateBottomBarSongInfo(song: com.musicplayer.pro.models.Song) {
        val titleText = bottomMusicBar.findViewById<android.widget.TextView>(R.id.miniSongTitle)
        val artistText = bottomMusicBar.findViewById<android.widget.TextView>(R.id.miniArtistName)
        val albumArt = bottomMusicBar.findViewById<android.widget.ImageView>(R.id.miniAlbumCover)
        val qualityIndicator = bottomMusicBar.findViewById<android.widget.TextView>(R.id.qualityIndicator)

        // تحديث النصوص مع رسوم متحركة
        titleText?.let {
            it.alpha = 0f
            it.text = song.title
            it.animate().alpha(1f).setDuration(200).start()

            // تفعيل التمرير للنصوص الطويلة
            it.isSelected = true
        }

        artistText?.let {
            it.alpha = 0f
            it.text = song.artist
            it.animate().alpha(1f).setDuration(200).start()
        }

        // تحديث صورة الألبوم
        albumArt?.let {
            com.musicplayer.pro.utils.ImageLoader.loadSongImage(
                context = this@MainActivity,
                song = song,
                imageView = it,
                placeholder = R.drawable.default_album_cover
            )
        }

        // إظهار مؤشر الجودة
        qualityIndicator?.let {
            if (song.size > 5 * 1024 * 1024) { // أكبر من 5 MB
                it.text = "HD"
                it.visibility = View.VISIBLE
            } else {
                it.visibility = View.GONE
            }
        }
    }

    /**
     * تحديث حالة التشغيل
     */
    private fun updateBottomBarPlaybackState(isPlaying: Boolean) {
        try {
            val playPauseButton = bottomMusicBar.findViewById<android.widget.ImageButton>(R.id.btnMiniPlayPause)
            val playingIndicator = bottomMusicBar.findViewById<View>(R.id.playingIndicator)
            val progressBar = bottomMusicBar.findViewById<android.widget.ProgressBar>(R.id.miniProgressBar)

            // تحديث أيقونة زر التشغيل مع رسوم متحركة
            playPauseButton?.let { button ->
                val iconRes = if (isPlaying) R.drawable.ic_pause else R.drawable.ic_play

                // رسوم متحركة للتبديل
                button.animate()
                    .scaleX(0.8f)
                    .scaleY(0.8f)
                    .setDuration(100)
                    .withEndAction {
                        button.setImageResource(iconRes)
                        button.animate()
                            .scaleX(1.0f)
                            .scaleY(1.0f)
                            .setDuration(100)
                            .start()
                    }
                    .start()
            }

            // تحديث مؤشر التشغيل
            playingIndicator?.let {
                if (isPlaying) {
                    it.visibility = View.VISIBLE
                    it.alpha = 0f
                    it.animate().alpha(1f).setDuration(200).start()
                } else {
                    it.animate().alpha(0f).setDuration(200).withEndAction {
                        it.visibility = View.GONE
                    }.start()
                }
            }

            // تحديث شريط التقدم
            if (isPlaying) {
                startProgressUpdates()
            }

        } catch (e: Exception) {
            Log.e("MainActivity", "خطأ في تحديث حالة التشغيل: ${e.message}")
        }
    }

    /**
     * بدء تحديث شريط التقدم
     */
    private fun startProgressUpdates() {
        stopProgressUpdates() // إيقاف التحديث السابق

        progressUpdateRunnable = object : Runnable {
            override fun run() {
                musicService?.let { service ->
                    if (service.isPlaying()) {
                        val currentPosition = service.getCurrentPosition()
                        val duration = service.getDuration()

                        if (duration > 0) {
                            updateProgress(currentPosition, duration)
                        }

                        // جدولة التحديث التالي
                        progressHandler.postDelayed(this, 1000)
                    }
                }
            }
        }

        progressUpdateRunnable?.let {
            progressHandler.post(it)
        }
    }

    /**
     * إيقاف تحديث شريط التقدم
     */
    private fun stopProgressUpdates() {
        progressUpdateRunnable?.let {
            progressHandler.removeCallbacks(it)
        }
        progressUpdateRunnable = null
    }

    /**
     * تحديث شريط التقدم يدوياً
     */
    fun updateProgress(position: Int, duration: Int) {
        runOnUiThread {
            val progressBar = bottomMusicBar.findViewById<android.widget.ProgressBar>(R.id.miniProgressBar)
            progressBar?.let {
                if (duration > 0) {
                    val progress = (position * 100 / duration).toInt()
                    it.progress = progress
                }
            }
        }
    }

    /**
     * إخفاء الشريط السفلي مع رسوم متحركة
     */
    fun hideBottomMusicBar() {
        if (bottomMusicBar.visibility == View.VISIBLE) {
            bottomMusicBar.animate()
                .translationY(bottomMusicBar.height.toFloat())
                .setDuration(300)
                .withEndAction {
                    bottomMusicBar.visibility = View.GONE
                    bottomMusicBar.translationY = 0f
                }
                .start()
        }
    }

    /**
     * إخفاء الشريط السفلي فوراً (بدون رسوم متحركة)
     */
    fun hideBottomMusicBarInstantly() {
        bottomMusicBar.visibility = View.GONE
    }

    /**
     * إظهار الشريط السفلي إذا كان هناك أغنية قيد التشغيل
     */
    fun showBottomMusicBarIfPlaying() {
        // التحقق من أننا لسنا في شاشة التشغيل الحالي
        if (viewPager.currentItem == 1) {
            return // لا نظهر الشريط في شاشة التشغيل الحالي
        }

        musicService?.getCurrentSong()?.let { song ->
            if (musicService?.isPlaying() == true || musicService?.getCurrentPosition() ?: 0 > 0) {
                showBottomMusicBar(song)
            }
        }
    }

    /**
     * الحصول على MusicService للاستخدام في الـ Fragments
     */
    fun getMusicService(): MusicService? {
        return musicService
    }

    /**
     * تحريك زر التشغيل/الإيقاف
     */
    private fun animatePlayButton(button: android.widget.ImageButton?) {
        button?.let {
            it.animate()
                .scaleX(0.9f)
                .scaleY(0.9f)
                .setDuration(100)
                .withEndAction {
                    it.animate()
                        .scaleX(1.0f)
                        .scaleY(1.0f)
                        .setDuration(100)
                        .start()
                }
                .start()
        }
    }



    /**
     * عرض شرح الأذونات قبل طلبها
     */
    private fun showPermissionExplanationDialog(permissions: Array<String>) {
        val permissionExplanations = getPermissionExplanations(permissions)

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle(getString(R.string.permissions_required))
            .setMessage(getString(R.string.permissions_explanation) + "\n\n" + permissionExplanations)
            .setIcon(R.drawable.ic_info)
            .setPositiveButton(getString(R.string.grant_permissions)) { _, _ ->
                permissionLauncher.launch(permissions)
            }
            .setNegativeButton(getString(R.string.exit)) { _, _ ->
                showPermissionDeniedMessage()
                finishAndRemoveTask()
            }
            .setCancelable(false)
            .show()
    }

    /**
     * الحصول على شرح الأذونات الأساسية
     */
    private fun getPermissionExplanations(permissions: Array<String>): String {
        val explanations = mutableListOf<String>()

        permissions.forEach { permission ->
            when (permission) {
                Manifest.permission.READ_EXTERNAL_STORAGE,
                Manifest.permission.READ_MEDIA_AUDIO -> {
                    if (!explanations.contains("📁 الوصول للملفات")) {
                        explanations.add("📁 الوصول للملفات: لقراءة ملفات الموسيقى من جهازك")
                    }
                }
                Manifest.permission.INTERNET,
                Manifest.permission.ACCESS_NETWORK_STATE -> {
                    if (!explanations.contains("🌐 الإنترنت")) {
                        explanations.add("🌐 الإنترنت: لتحميل الموسيقى من YouTube وSoundCloud")
                    }
                }
                Manifest.permission.POST_NOTIFICATIONS -> {
                    explanations.add("🔔 الإشعارات: لعرض أدوات التحكم في التشغيل")
                }
                Manifest.permission.MODIFY_AUDIO_SETTINGS -> {
                    explanations.add("🎵 إعدادات الصوت: للمعادل الصوتي وتحسين جودة الصوت")
                }
            }
        }

        return explanations.joinToString("\n")
    }

    /**
     * عرض رسالة رفض الأذونات
     */
    private fun showPermissionDeniedMessage() {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle(getString(R.string.permissions_denied))
            .setMessage(getString(R.string.permissions_denied_message))
            .setIcon(R.drawable.ic_warning)
            .setPositiveButton(getString(R.string.open_settings)) { _, _ ->
                openAppSettings()
            }
            .setNegativeButton(getString(R.string.exit)) { _, _ ->
                finishAndRemoveTask()
            }
            .setCancelable(false)
            .show()
    }

    /**
     * فتح إعدادات التطبيق
     */
    private fun openAppSettings() {
        try {
            val intent = android.content.Intent(android.provider.Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
            intent.data = android.net.Uri.fromParts("package", packageName, null)
            startActivity(intent)
        } catch (e: Exception) {
            Toast.makeText(this, "لا يمكن فتح الإعدادات", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * عرض نتائج الأذونات
     */
    private fun showPermissionResults(granted: Set<String>, denied: Set<String>) {
        if (denied.isNotEmpty()) {
            val deniedFeatures = mutableListOf<String>()

            denied.forEach { permission ->
                when (permission) {
                    Manifest.permission.POST_NOTIFICATIONS -> deniedFeatures.add("🔔 إشعارات التحكم في التشغيل")
                    Manifest.permission.MODIFY_AUDIO_SETTINGS -> deniedFeatures.add("🎵 المعادل الصوتي وتحسين الصوت")
                }
            }

            if (deniedFeatures.isNotEmpty()) {
                val message = "تم منح الأذونات الأساسية للتشغيل ✅\n\nالميزات المحدودة:\n" +
                             deniedFeatures.joinToString("\n") +
                             "\n\nيمكنك تفعيل هذه الميزات لاحقاً من إعدادات الجهاز."

                androidx.appcompat.app.AlertDialog.Builder(this)
                    .setTitle("معلومات الأذونات")
                    .setMessage(message)
                    .setIcon(R.drawable.ic_info)
                    .setPositiveButton(getString(R.string.ok), null)
                    .show()
            }
        } else {
            Toast.makeText(this, "تم منح جميع الأذونات المطلوبة ✅", Toast.LENGTH_LONG).show()
        }
    }

    /**
     * عرض رسالة رفض الأذونات الأساسية
     */
    private fun showCriticalPermissionDeniedMessage() {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("أذونات أساسية مطلوبة")
            .setMessage("لا يمكن للتطبيق العمل بدون الأذونات الأساسية التالية:\n\n📁 قراءة ملفات الموسيقى\n🌐 الاتصال بالإنترنت\n\nهذه الأذونات ضرورية لتشغيل الموسيقى وتحميل المحتوى.")
            .setIcon(R.drawable.ic_warning)
            .setPositiveButton(getString(R.string.open_settings)) { _, _ ->
                openAppSettings()
            }
            .setNegativeButton(getString(R.string.exit)) { _, _ ->
                finishAndRemoveTask()
            }
            .setCancelable(false)
            .show()
    }

    /**
     * فتح شاشة الإعدادات
     */
    private fun openSettings() {
        val intent = android.content.Intent(this, SettingsActivity::class.java)
        startActivity(intent)
    }

    /**
     * عرض dialog حول التطبيق
     */
    private fun showAboutDialog() {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle(getString(R.string.about))
            .setMessage(getString(R.string.about_message))
            .setIcon(R.drawable.ic_music_note)
            .setPositiveButton(getString(R.string.ok), null)
            .show()
    }

    override fun onResume() {
        super.onResume()
        // فحص الأذونات عند العودة للتطبيق
        checkPermissionsOnResume()
    }

    override fun onDestroy() {
        super.onDestroy()

        // إيقاف تحديثات التقدم
        stopProgressUpdates()

        // إلغاء ربط الخدمة
        if (isServiceBound) {
            unbindService(serviceConnection)
            isServiceBound = false
        }
    }

    /**
     * فحص الأذونات عند العودة للتطبيق
     */
    private fun checkPermissionsOnResume() {
        val essentialPermissions = mutableListOf<String>().apply {
            // إذن قراءة الملفات (حسب إصدار الأندرويد)
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
                add(Manifest.permission.READ_MEDIA_AUDIO)
            } else {
                add(Manifest.permission.READ_EXTERNAL_STORAGE)
            }
            // أذونات الشبكة
            add(Manifest.permission.INTERNET)
            add(Manifest.permission.ACCESS_NETWORK_STATE)
        }

        val missingEssential = essentialPermissions.filter {
            ContextCompat.checkSelfPermission(this, it) != PackageManager.PERMISSION_GRANTED
        }

        if (missingEssential.isNotEmpty()) {
            // إذا فُقدت أذونات أساسية، اطلبها مرة أخرى
            requestNecessaryPermissions()
        }
    }

    /**
     * Adapter للـ ViewPager
     */
    private class MainPagerAdapter(activity: AppCompatActivity) : FragmentStateAdapter(activity) {

        override fun getItemCount(): Int = 3

        override fun createFragment(position: Int): Fragment {
            return when (position) {
                0 -> MainFragment()
                1 -> NowPlayingFragment()
                2 -> DownloadFragment()
                else -> MainFragment()
            }
        }
    }
}
