// Generated by view binder compiler. Do not edit!
package com.musicplayer.pro.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.musicplayer.pro.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentDownloadBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final LinearLayout emptyStateLayout;

  @NonNull
  public final FloatingActionButton fabAddDownload;

  @NonNull
  public final RecyclerView recyclerViewDownloads;

  private FragmentDownloadBinding(@NonNull CoordinatorLayout rootView,
      @NonNull LinearLayout emptyStateLayout, @NonNull FloatingActionButton fabAddDownload,
      @NonNull RecyclerView recyclerViewDownloads) {
    this.rootView = rootView;
    this.emptyStateLayout = emptyStateLayout;
    this.fabAddDownload = fabAddDownload;
    this.recyclerViewDownloads = recyclerViewDownloads;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentDownloadBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentDownloadBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_download, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentDownloadBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.emptyStateLayout;
      LinearLayout emptyStateLayout = ViewBindings.findChildViewById(rootView, id);
      if (emptyStateLayout == null) {
        break missingId;
      }

      id = R.id.fabAddDownload;
      FloatingActionButton fabAddDownload = ViewBindings.findChildViewById(rootView, id);
      if (fabAddDownload == null) {
        break missingId;
      }

      id = R.id.recyclerViewDownloads;
      RecyclerView recyclerViewDownloads = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewDownloads == null) {
        break missingId;
      }

      return new FragmentDownloadBinding((CoordinatorLayout) rootView, emptyStateLayout,
          fabAddDownload, recyclerViewDownloads);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
