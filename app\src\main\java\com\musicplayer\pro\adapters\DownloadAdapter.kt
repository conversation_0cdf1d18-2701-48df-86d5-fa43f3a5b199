package com.musicplayer.pro.adapters

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.recyclerview.widget.RecyclerView
import com.musicplayer.pro.R
import com.musicplayer.pro.models.Download
import com.musicplayer.pro.models.DownloadStatus

/**
 * محول قائمة التحميلات
 */
class DownloadAdapter(
    private val onDownloadClick: (Download) -> Unit,
    private val onPauseClick: (Download) -> Unit,
    private val onResumeClick: (Download) -> Unit,
    private val onCancelClick: (Download) -> Unit,
    private val onRetryClick: (Download) -> Unit,
    private val onDeleteClick: (Download) -> Unit
) : RecyclerView.Adapter<DownloadAdapter.DownloadViewHolder>() {
    
    private var downloads: List<Download> = emptyList()
    
    override fun onCreateViewHolder(parent: <PERSON>Group, viewType: Int): DownloadViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_download, parent, false)
        return DownloadViewHolder(view)
    }
    
    override fun onBindViewHolder(holder: DownloadViewHolder, position: Int) {
        val download = downloads[position]
        holder.bind(download)
    }
    
    override fun getItemCount(): Int = downloads.size
    
    /**
     * تحديث قائمة التحميلات
     */
    fun updateDownloads(newDownloads: List<Download>) {
        downloads = newDownloads
        notifyDataSetChanged()
    }
    
    inner class DownloadViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val titleTextView: TextView = itemView.findViewById(R.id.downloadTitle)
        private val artistTextView: TextView = itemView.findViewById(R.id.downloadArtist)
        private val statusTextView: TextView = itemView.findViewById(R.id.downloadStatus)
        private val progressBar: ProgressBar = itemView.findViewById(R.id.downloadProgress)
        private val progressTextView: TextView = itemView.findViewById(R.id.progressText)
        private val sizeTextView: TextView = itemView.findViewById(R.id.downloadSize)
        private val speedTextView: TextView = itemView.findViewById(R.id.downloadSpeed)
        private val etaTextView: TextView = itemView.findViewById(R.id.downloadEta)
        private val thumbnailImageView: ImageView = itemView.findViewById(R.id.downloadThumbnail)
        private val actionButton: ImageButton = itemView.findViewById(R.id.actionButton)
        private val deleteButton: ImageButton = itemView.findViewById(R.id.deleteButton)
        
        fun bind(download: Download) {
            titleTextView.text = download.title
            artistTextView.text = download.artist.ifEmpty { "Unknown Artist" }
            statusTextView.text = getStatusText(download.status)
            
            // تحديث شريط التقدم
            progressBar.progress = download.getProgressPercentage()
            progressTextView.text = "${download.getProgressPercentage()}%"
            
            // تحديث معلومات الحجم والسرعة
            sizeTextView.text = "${download.getFormattedDownloadedSize()} / ${download.getFormattedTotalSize()}"
            speedTextView.text = download.speed.ifEmpty { "-" }
            etaTextView.text = download.eta.ifEmpty { "-" }
            
            // تحديث الصورة المصغرة
            if (download.thumbnail.isNullOrEmpty()) {
                thumbnailImageView.setImageResource(R.drawable.default_album_cover)
            } else {
                // يمكن استخدام Glide لتحميل الصورة
                thumbnailImageView.setImageResource(R.drawable.default_album_cover)
            }
            
            // تحديث زر العمل حسب الحالة
            updateActionButton(download)
            
            // إعداد المستمعين
            itemView.setOnClickListener { onDownloadClick(download) }
            deleteButton.setOnClickListener { onDeleteClick(download) }
        }
        
        private fun updateActionButton(download: Download) {
            when (download.status) {
                DownloadStatus.PENDING -> {
                    actionButton.setImageResource(R.drawable.ic_pause)
                    actionButton.setOnClickListener { onPauseClick(download) }
                    actionButton.visibility = View.VISIBLE
                }
                DownloadStatus.DOWNLOADING -> {
                    actionButton.setImageResource(R.drawable.ic_pause)
                    actionButton.setOnClickListener { onPauseClick(download) }
                    actionButton.visibility = View.VISIBLE
                }
                DownloadStatus.PAUSED -> {
                    actionButton.setImageResource(R.drawable.ic_play)
                    actionButton.setOnClickListener { onResumeClick(download) }
                    actionButton.visibility = View.VISIBLE
                }
                DownloadStatus.COMPLETED -> {
                    actionButton.setImageResource(R.drawable.ic_play)
                    actionButton.setOnClickListener { onDownloadClick(download) }
                    actionButton.visibility = View.VISIBLE
                }
                DownloadStatus.FAILED -> {
                    actionButton.setImageResource(R.drawable.ic_refresh)
                    actionButton.setOnClickListener { onRetryClick(download) }
                    actionButton.visibility = View.VISIBLE
                }
                DownloadStatus.CANCELLED -> {
                    actionButton.setImageResource(R.drawable.ic_refresh)
                    actionButton.setOnClickListener { onRetryClick(download) }
                    actionButton.visibility = View.VISIBLE
                }
            }
        }
        
        private fun getStatusText(status: DownloadStatus): String {
            return when (status) {
                DownloadStatus.PENDING -> "في الانتظار"
                DownloadStatus.DOWNLOADING -> "جاري التحميل"
                DownloadStatus.PAUSED -> "متوقف مؤقتاً"
                DownloadStatus.COMPLETED -> "مكتمل"
                DownloadStatus.FAILED -> "فاشل"
                DownloadStatus.CANCELLED -> "ملغي"
            }
        }
    }
}
