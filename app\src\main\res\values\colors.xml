<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Material Design Colors -->
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFFFF</color>

    <!-- Music Player Theme Colors -->
    <!-- Primary Colors -->
    <color name="accent_primary">#FF6200EE</color>
    <color name="accent_secondary">#FF3700B3</color>

    <!-- Background Colors -->
    <color name="background_primary">#FFFFFF</color>
    <color name="background_secondary">#F5F5F5</color>
    <color name="surface_primary">#FFFFFF</color>
    <color name="surface_secondary">#F8F9FA</color>
    <color name="surface_tertiary">#E0E0E0</color>

    <!-- Text Colors -->
    <color name="text_primary">#212121</color>
    <color name="text_secondary">#757575</color>
    <color name="text_tertiary">#BDBDBD</color>
    <color name="text_on_accent">#FFFFFF</color>

    <!-- Status Colors -->
    <color name="success">#4CAF50</color>
    <color name="warning">#FF9800</color>
    <color name="error">#F44336</color>
    <color name="info">#2196F3</color>

    <!-- Dark Theme Colors -->
    <color name="background_primary_dark">#121212</color>
    <color name="background_secondary_dark">#1E1E1E</color>
    <color name="surface_primary_dark">#1E1E1E</color>
    <color name="surface_secondary_dark">#2D2D2D</color>
    <color name="text_primary_dark">#FFFFFF</color>
    <color name="text_secondary_dark">#B3B3B3</color>
    <color name="text_tertiary_dark">#666666</color>

    <!-- Transparent Colors -->
    <color name="transparent">#00000000</color>
    <color name="semi_transparent_black">#80000000</color>
    <color name="semi_transparent_white">#80FFFFFF</color>
</resources>
