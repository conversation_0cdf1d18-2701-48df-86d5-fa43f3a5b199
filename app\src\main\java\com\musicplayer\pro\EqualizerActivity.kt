package com.musicplayer.pro

import android.os.Bundle
import android.widget.SeekBar
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.musicplayer.pro.adapters.EqualizerAdapter
import com.musicplayer.pro.managers.AudioEffectsManager
import com.musicplayer.pro.models.EqualizerBand

/**
 * نشاط المعادل الصوتي المخصص
 * مطابق لميزات المعادل في Python
 */
class EqualizerActivity : AppCompatActivity() {
    
    private lateinit var audioEffectsManager: AudioEffectsManager
    private lateinit var recyclerView: RecyclerView
    private lateinit var equalizerAdapter: EqualizerAdapter
    private lateinit var bassBoostSeekBar: SeekBar
    private lateinit var virtualizerSeekBar: SeekBar
    private lateinit var bassBoostLabel: TextView
    private lateinit var virtualizerLabel: TextView
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_equalizer)
        
        // إعداد شريط الأدوات
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            title = getString(R.string.equalizer)
        }
        
        // تهيئة المدير
        audioEffectsManager = AudioEffectsManager(this)
        
        // تهيئة UI
        initializeUI()
        
        // إعداد المعادل
        setupEqualizer()
    }
    
    /**
     * تهيئة واجهة المستخدم
     */
    private fun initializeUI() {
        recyclerView = findViewById(R.id.recyclerViewEqualizer)
        bassBoostSeekBar = findViewById(R.id.bassBoostSeekBar)
        virtualizerSeekBar = findViewById(R.id.virtualizerSeekBar)
        bassBoostLabel = findViewById(R.id.bassBoostLabel)
        virtualizerLabel = findViewById(R.id.virtualizerLabel)
        
        setupRecyclerView()
        setupEffectControls()
    }
    
    /**
     * إعداد RecyclerView للمعادل
     */
    private fun setupRecyclerView() {
        equalizerAdapter = EqualizerAdapter { band, level ->
            audioEffectsManager.setBandLevel(band.bandIndex, level)
        }
        
        recyclerView.apply {
            layoutManager = LinearLayoutManager(this@EqualizerActivity)
            adapter = equalizerAdapter
        }
    }
    
    /**
     * إعداد أدوات التحكم في التأثيرات
     */
    private fun setupEffectControls() {
        // Bass Boost
        bassBoostSeekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    audioEffectsManager.setBassBoostEnabled(progress > 0)
                    bassBoostLabel.text = "Bass Boost: $progress%"
                }
            }
            
            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })
        
        // Virtualizer
        virtualizerSeekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    audioEffectsManager.setVirtualizerEnabled(progress > 0)
                    virtualizerLabel.text = "Virtualizer: $progress%"
                }
            }
            
            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })
    }
    
    /**
     * إعداد المعادل
     */
    private fun setupEqualizer() {
        val numberOfBands = audioEffectsManager.getNumberOfBands()
        val bands = mutableListOf<EqualizerBand>()
        
        for (i in 0 until numberOfBands) {
            val freqRange = audioEffectsManager.getBandFreqRange(i.toShort())
            val centerFreq = freqRange?.let { (it[0] + it[1]) / 2 } ?: 0
            val currentLevel = audioEffectsManager.getBandLevel(i.toShort())

            bands.add(
                EqualizerBand(
                    bandIndex = i.toShort(),
                    frequency = centerFreq,
                    level = currentLevel,
                    minLevel = -1500, // -15 dB
                    maxLevel = 1500   // +15 dB
                )
            )
        }
        
        equalizerAdapter.updateBands(bands)
    }
    
    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
    
    override fun onDestroy() {
        super.onDestroy()
        audioEffectsManager.release()
    }
}
