-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:181:9-189:20
	android:grantUriPermissions
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:185:13-47
	android:authorities
		INJECTED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:183:13-64
	android:exported
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:184:13-37
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:182:13-62
manifest
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:2:1-228:12
INJECTED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:2:1-228:12
INJECTED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:2:1-228:12
INJECTED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:2:1-228:12
MERGED from [androidx.databinding:viewbinding:8.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\accd01e0544adc9ddd383a622c66577d\transformed\jetified-viewbinding-8.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b7043eedc50cada365136aa0e9ac9a0\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:17:1-32:12
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c5338133c2ae694e9daa17392b3eff80\transformed\navigation-common-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b23a0fdf8fa6b1b1b94ff3ada8e9ae79\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3334a6fe5d2634fb81b47179e880dc14\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6dc89a3bb2c121e6fd05a6739bf2801\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f46d50c21bdb071c2287ad7071d69e3\transformed\navigation-fragment-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\312f1de20f94fa1ab82edc284a3b1234\transformed\navigation-fragment-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1540200393a9784757a2afa6e319dea2\transformed\navigation-ui-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e913642d7c50f47db3e63580f9572497\transformed\navigation-ui-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e081d70774c49b9b7dc6950692deb52\transformed\material-1.11.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1acb68811f521c1987c5f4fbd1b2b8d8\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [com.airbnb.android:lottie:6.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06456d44bb4bdd02de302c8f98505246\transformed\jetified-lottie-6.2.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbdadc0fe00c730990fd062b00ebae23\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.preference:preference-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\70fe8d58ee623ccd77c4859e4c2f55bd\transformed\jetified-preference-ktx-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\210f3162f1672bd532c02d8af1bd50c1\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0962e3cd95d785309666b77b5473090\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [jp.wasabeef:glide-transformations:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8970dfe59859a56e6f396ea43ae4bb7a\transformed\jetified-glide-transformations-4.3.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aaf25400bb7e1ec27daea7dc9f3df6b9\transformed\jetified-glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.media3:media3-exoplayer:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0c4d03d60aaed2968a93a146657af49\transformed\jetified-media3-exoplayer-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-extractor:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2b2a6bc81d69ca6212c07484fc021cf\transformed\jetified-media3-extractor-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1eafd7cdf730f4e007a017586b6c9568\transformed\jetified-media3-container-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d678965a4ecfacf23baedd0880df7958\transformed\jetified-media3-datasource-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f40af842a9a4c65028313a84ec1d8f7\transformed\jetified-media3-decoder-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5e935e35471f76140cabfece0cca49e\transformed\jetified-media3-database-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f903d2d5b1472b181479b86907310f0e\transformed\jetified-media3-common-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-ui:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\472040ed58573b5e54239e60b254baef\transformed\jetified-media3-ui-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b329f76eb4d897ae4ccbf4d5d1e9d060\transformed\recyclerview-1.3.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04ddc434742f2313e237bac0a66fcee1\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a141a1f7ed03f5747ee942445c6fcc7\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f38bfda3f7bcc90f86782212071775bd\transformed\jetified-fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a9bebf8e5bbb464539d2f1c3751a881\transformed\jetified-activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\14a3e82cceefba0de61ff34573e0919e\transformed\jetified-activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e6d558491d75611da77b86b8bf4ede4\transformed\media-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1161f0a8562187a0c56ef89266bbe2e\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7eec3ffd9134b39a33221bcb756c708\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6e1ecf787ba6ce06fa3f517ea2d03c8\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5c12ae7945d45ea077704eef99e3ed9\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b260ca8e4027972f84f9b96089a2042e\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3163277d5a931e78681c2ed1ad88c00\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a74ef3bec649a4b6aa4b5971933d0b91\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.palette:palette-ktx:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\166d0fbbb52470a485b7716ec68b28c4\transformed\jetified-palette-ktx-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.palette:palette:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7e7e3b48202b6927c1f20fec059f7459\transformed\palette-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40aecd3cc962e8b264883814ae5a2fd0\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\906493b48761c290243a737adfc0eb3a\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f58464ea0003e8effdc4bb405eefaa5\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d7b75ca0408efcb2e6fb262d213dd35f\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2467365d615c8f4d74985839be4e3a72\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6c67302a26437d67690b4de1a6c0ac5\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6b7dbe88d3a04c6d55aa7c581f20597\transformed\jetified-window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c108dd56627f30fe94755d1a2faeaf2\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5c72dbf7ab217c6ae0efd6d2ecc3ef52\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d219e3c7c24445de7d034f0ee27187bf\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7362735739f227c043f1b1c8915d2b18\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7115d9f9b930696a861e118f0dba3e64\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47b54bf287343bbce7eafca9f7c47b52\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06ebe832fe25647990d118df0bb01aab\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f2aa0fe1bbaeecf299c06bc7c5baadf\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ee40adaae230d5d7ac7fb7ac3d13327\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\391dcfbb653a3c36bffe25fdfdb14322\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\16a9e14541efe46b4c93038dbbb0fa37\transformed\jetified-lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\add5756475d353f3d2526d69c987d8ce\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e301eb0149c4dcf3d8f0b2c16fc56c0\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71d99d0694eeca94e20096f2777d039d\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d552aa603d1087ef646d07674c8196c9\transformed\jetified-core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78f81a6d22eb336d8ab0e25f80a24035\transformed\jetified-annotation-experimental-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba34744bec8d4220a6118a6c2670b4a8\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e6807f253dd82e862a5356e763b3b35\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d18b3c4463a71c8eff89874c1db57286\transformed\jetified-gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1a9b06bc28deabc21eb44e4ca38562\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b0e68efff417ea48090e3b4aa9b30dda\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c19a4bdd040db89b795fe7f484312ec4\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382da296a152b21823bfc19980aa0be4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ff4466173ff565df36c2f41f0366bc7\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ca003ed9c217c662075a7d5b908039fd\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e39ca6cc1bda455f5d2c5feefe850ab\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07c22a9090ac8adfcfa0bb4291cf058f\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cba683e472751d3d4b17e60341ee8264\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:6:5-67
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:7:5-79
MERGED from [androidx.media3:media3-exoplayer:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0c4d03d60aaed2968a93a146657af49\transformed\jetified-media3-exoplayer-1.2.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0c4d03d60aaed2968a93a146657af49\transformed\jetified-media3-exoplayer-1.2.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f903d2d5b1472b181479b86907310f0e\transformed\jetified-media3-common-1.2.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f903d2d5b1472b181479b86907310f0e\transformed\jetified-media3-common-1.2.1\AndroidManifest.xml:22:5-79
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:8:5-76
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:8:22-73
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:11:5-80
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:11:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:12:5-13:38
	android:maxSdkVersion
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:13:9-35
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:12:22-78
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:14:5-15:40
	tools:ignore
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:15:9-37
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:14:22-79
uses-permission#android.permission.READ_MEDIA_AUDIO
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:16:5-75
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:16:22-72
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:19:5-80
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:19:22-77
uses-permission#android.permission.RECORD_AUDIO
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:20:5-71
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:20:22-68
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:23:5-77
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:23:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:24:5-92
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:24:22-89
uses-permission#android.permission.WAKE_LOCK
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:25:5-68
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:25:22-65
uses-permission#android.permission.VIBRATE
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:26:5-66
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:26:22-63
uses-permission#android.permission.BLUETOOTH
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:29:5-68
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:29:22-65
uses-permission#android.permission.BLUETOOTH_ADMIN
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:30:5-74
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:30:22-71
uses-permission#android.permission.BLUETOOTH_CONNECT
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:31:5-76
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:31:22-73
uses-permission#android.permission.BLUETOOTH_SCAN
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:32:5-73
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:32:22-70
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:35:5-77
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:35:22-74
uses-permission#android.permission.ACCESS_NOTIFICATION_POLICY
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:36:5-85
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:36:22-82
uses-permission#android.permission.BIND_NOTIFICATION_LISTENER_SERVICE
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:37:5-93
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:37:22-90
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:40:5-81
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:40:22-78
uses-permission#android.permission.READ_PHONE_STATE
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:41:5-75
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:41:22-72
uses-permission#android.permission.MEDIA_CONTENT_CONTROL
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:42:5-80
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:42:22-77
uses-feature#android.hardware.audio.output
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:45:5-47:35
	android:required
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:47:9-32
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:46:9-53
uses-feature#android.hardware.microphone
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:48:5-50:36
	android:required
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:50:9-33
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:49:9-51
uses-feature#android.hardware.bluetooth
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:51:5-53:36
	android:required
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:53:9-33
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:52:9-50
uses-feature#android.hardware.wifi
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:54:5-56:36
	android:required
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:56:9-33
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:55:9-45
application
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:58:5-211:19
INJECTED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:58:5-211:19
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b7043eedc50cada365136aa0e9ac9a0\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:26:5-30:19
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b7043eedc50cada365136aa0e9ac9a0\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:26:5-30:19
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e081d70774c49b9b7dc6950692deb52\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e081d70774c49b9b7dc6950692deb52\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1acb68811f521c1987c5f4fbd1b2b8d8\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1acb68811f521c1987c5f4fbd1b2b8d8\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [com.airbnb.android:lottie:6.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06456d44bb4bdd02de302c8f98505246\transformed\jetified-lottie-6.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.airbnb.android:lottie:6.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06456d44bb4bdd02de302c8f98505246\transformed\jetified-lottie-6.2.0\AndroidManifest.xml:7:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7eec3ffd9134b39a33221bcb756c708\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7eec3ffd9134b39a33221bcb756c708\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6b7dbe88d3a04c6d55aa7c581f20597\transformed\jetified-window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6b7dbe88d3a04c6d55aa7c581f20597\transformed\jetified-window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c108dd56627f30fe94755d1a2faeaf2\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c108dd56627f30fe94755d1a2faeaf2\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47b54bf287343bbce7eafca9f7c47b52\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47b54bf287343bbce7eafca9f7c47b52\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b0e68efff417ea48090e3b4aa9b30dda\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b0e68efff417ea48090e3b4aa9b30dda\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382da296a152b21823bfc19980aa0be4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382da296a152b21823bfc19980aa0be4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml
	android:requestLegacyExternalStorage
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:68:9-52
	android:roundIcon
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:65:9-54
	android:icon
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:63:9-43
	android:preserveLegacyExternalStorage
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:69:9-53
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c108dd56627f30fe94755d1a2faeaf2\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:66:9-35
	android:label
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:64:9-41
	android:fullBackupContent
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:62:9-54
	tools:targetApi
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:70:9-29
	android:allowBackup
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:60:9-35
	android:theme
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:67:9-52
	android:dataExtractionRules
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:61:9-65
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:59:9-47
activity#com.musicplayer.pro.MainActivity
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:73:9-104:20
	android:screenOrientation
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:77:13-49
	android:launchMode
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:76:13-43
	android:exported
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:75:13-36
	android:theme
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:78:13-56
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:74:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:79:13-82:29
action#android.intent.action.MAIN
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:80:17-69
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:80:25-66
category#android.intent.category.LAUNCHER
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:81:17-77
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:81:27-74
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:mimeType:audio/*
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:85:13-90:29
action#android.intent.action.VIEW
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:86:17-69
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:86:25-66
category#android.intent.category.DEFAULT
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:87:17-76
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:87:27-73
category#android.intent.category.BROWSABLE
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:88:17-78
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:88:27-75
data
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:89:17-52
	android:host
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:97:45-71
	android:scheme
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:97:23-44
	android:mimeType
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:89:23-49
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:www.youtube.com+data:host:www.youtube.com+data:host:youtu.be+data:host:youtu.be+data:host:youtube.com+data:host:youtube.com+data:scheme:http+data:scheme:http+data:scheme:http+data:scheme:https+data:scheme:https+data:scheme:https
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:93:13-103:29
	android:autoVerify
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:93:28-53
activity#com.musicplayer.pro.SettingsActivity
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:107:9-115:20
	android:parentActivityName
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:111:13-55
	android:exported
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:109:13-37
	android:theme
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:110:13-56
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:108:13-45
meta-data#android.support.PARENT_ACTIVITY
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:112:13-114:49
	android:value
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:114:17-46
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:113:17-63
activity#com.musicplayer.pro.EqualizerActivity
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:118:9-126:20
	android:parentActivityName
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:122:13-59
	android:exported
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:120:13-37
	android:theme
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:121:13-56
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:119:13-46
service#com.musicplayer.pro.services.MusicService
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:129:9-137:19
	android:enabled
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:131:13-35
	android:exported
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:132:13-37
	android:foregroundServiceType
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:133:13-58
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:130:13-50
intent-filter#action:name:android.media.browse.MediaBrowserService
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:134:13-136:29
action#android.media.browse.MediaBrowserService
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:135:17-83
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:135:25-80
service#com.musicplayer.pro.services.DownloadService
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:140:9-144:56
	android:enabled
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:142:13-35
	android:exported
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:143:13-37
	android:foregroundServiceType
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:144:13-53
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:141:13-53
receiver#com.musicplayer.pro.receivers.MediaButtonReceiver
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:147:9-154:20
	android:enabled
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:149:13-35
	android:exported
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:150:13-36
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:148:13-58
intent-filter#action:name:android.intent.action.MEDIA_BUTTON
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:151:13-153:29
	android:priority
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:151:28-51
action#android.intent.action.MEDIA_BUTTON
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:152:17-77
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:152:25-74
receiver#com.musicplayer.pro.receivers.BootReceiver
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:157:9-167:20
	android:enabled
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:159:13-35
	android:exported
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:160:13-36
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:158:13-51
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.MY_PACKAGE_REPLACED+action:name:android.intent.action.PACKAGE_REPLACED+data:scheme:package
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:161:13-166:29
	android:priority
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:161:28-51
action#android.intent.action.BOOT_COMPLETED
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:162:17-79
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:162:25-76
action#android.intent.action.MY_PACKAGE_REPLACED
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:163:17-84
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:163:25-81
action#android.intent.action.PACKAGE_REPLACED
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:164:17-81
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:164:25-78
receiver#com.musicplayer.pro.receivers.HeadsetReceiver
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:170:9-178:20
	android:enabled
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:172:13-35
	android:exported
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:173:13-36
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:171:13-54
intent-filter#action:name:android.bluetooth.a2dp.profile.action.CONNECTION_STATE_CHANGED+action:name:android.intent.action.HEADSET_PLUG
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:174:13-177:29
action#android.intent.action.HEADSET_PLUG
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:175:17-77
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:175:25-74
action#android.bluetooth.a2dp.profile.action.CONNECTION_STATE_CHANGED
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:176:17-105
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:176:25-102
meta-data#android.media.session.MediaSession
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:192:9-194:36
	android:value
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:194:13-33
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:193:13-62
meta-data#com.google.android.gms.car.application
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:197:9-199:59
	android:resource
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:199:13-56
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:198:13-66
meta-data#com.google.android.wearable.standalone
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:202:9-204:37
	android:value
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:204:13-34
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:203:13-66
meta-data#android.webkit.WebView.MetricsOptOut
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:207:9-209:36
	android:value
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:209:13-33
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:208:13-64
uses-feature#android.hardware.type.automotive
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:214:5-216:36
	android:required
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:216:9-33
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:215:9-56
uses-feature#android.software.leanback
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:219:5-221:36
	android:required
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:221:9-33
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:220:9-49
uses-feature#android.hardware.type.watch
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:224:5-226:36
	android:required
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:226:9-33
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:225:9-51
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:186:13-188:54
	android:resource
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:188:17-51
	android:name
		ADDED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:187:17-67
uses-sdk
INJECTED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml
INJECTED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\accd01e0544adc9ddd383a622c66577d\transformed\jetified-viewbinding-8.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\accd01e0544adc9ddd383a622c66577d\transformed\jetified-viewbinding-8.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b7043eedc50cada365136aa0e9ac9a0\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:22:5-24:41
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b7043eedc50cada365136aa0e9ac9a0\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:22:5-24:41
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c5338133c2ae694e9daa17392b3eff80\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c5338133c2ae694e9daa17392b3eff80\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b23a0fdf8fa6b1b1b94ff3ada8e9ae79\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b23a0fdf8fa6b1b1b94ff3ada8e9ae79\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3334a6fe5d2634fb81b47179e880dc14\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3334a6fe5d2634fb81b47179e880dc14\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6dc89a3bb2c121e6fd05a6739bf2801\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6dc89a3bb2c121e6fd05a6739bf2801\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f46d50c21bdb071c2287ad7071d69e3\transformed\navigation-fragment-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f46d50c21bdb071c2287ad7071d69e3\transformed\navigation-fragment-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\312f1de20f94fa1ab82edc284a3b1234\transformed\navigation-fragment-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\312f1de20f94fa1ab82edc284a3b1234\transformed\navigation-fragment-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1540200393a9784757a2afa6e319dea2\transformed\navigation-ui-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1540200393a9784757a2afa6e319dea2\transformed\navigation-ui-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e913642d7c50f47db3e63580f9572497\transformed\navigation-ui-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e913642d7c50f47db3e63580f9572497\transformed\navigation-ui-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e081d70774c49b9b7dc6950692deb52\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e081d70774c49b9b7dc6950692deb52\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1acb68811f521c1987c5f4fbd1b2b8d8\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1acb68811f521c1987c5f4fbd1b2b8d8\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [com.airbnb.android:lottie:6.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06456d44bb4bdd02de302c8f98505246\transformed\jetified-lottie-6.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06456d44bb4bdd02de302c8f98505246\transformed\jetified-lottie-6.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbdadc0fe00c730990fd062b00ebae23\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbdadc0fe00c730990fd062b00ebae23\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.preference:preference-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\70fe8d58ee623ccd77c4859e4c2f55bd\transformed\jetified-preference-ktx-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\70fe8d58ee623ccd77c4859e4c2f55bd\transformed\jetified-preference-ktx-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\210f3162f1672bd532c02d8af1bd50c1\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\210f3162f1672bd532c02d8af1bd50c1\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0962e3cd95d785309666b77b5473090\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0962e3cd95d785309666b77b5473090\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [jp.wasabeef:glide-transformations:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8970dfe59859a56e6f396ea43ae4bb7a\transformed\jetified-glide-transformations-4.3.0\AndroidManifest.xml:5:5-7:41
MERGED from [jp.wasabeef:glide-transformations:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8970dfe59859a56e6f396ea43ae4bb7a\transformed\jetified-glide-transformations-4.3.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aaf25400bb7e1ec27daea7dc9f3df6b9\transformed\jetified-glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aaf25400bb7e1ec27daea7dc9f3df6b9\transformed\jetified-glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.media3:media3-exoplayer:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0c4d03d60aaed2968a93a146657af49\transformed\jetified-media3-exoplayer-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0c4d03d60aaed2968a93a146657af49\transformed\jetified-media3-exoplayer-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2b2a6bc81d69ca6212c07484fc021cf\transformed\jetified-media3-extractor-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2b2a6bc81d69ca6212c07484fc021cf\transformed\jetified-media3-extractor-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1eafd7cdf730f4e007a017586b6c9568\transformed\jetified-media3-container-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1eafd7cdf730f4e007a017586b6c9568\transformed\jetified-media3-container-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d678965a4ecfacf23baedd0880df7958\transformed\jetified-media3-datasource-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d678965a4ecfacf23baedd0880df7958\transformed\jetified-media3-datasource-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f40af842a9a4c65028313a84ec1d8f7\transformed\jetified-media3-decoder-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f40af842a9a4c65028313a84ec1d8f7\transformed\jetified-media3-decoder-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5e935e35471f76140cabfece0cca49e\transformed\jetified-media3-database-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5e935e35471f76140cabfece0cca49e\transformed\jetified-media3-database-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f903d2d5b1472b181479b86907310f0e\transformed\jetified-media3-common-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f903d2d5b1472b181479b86907310f0e\transformed\jetified-media3-common-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\472040ed58573b5e54239e60b254baef\transformed\jetified-media3-ui-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\472040ed58573b5e54239e60b254baef\transformed\jetified-media3-ui-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b329f76eb4d897ae4ccbf4d5d1e9d060\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b329f76eb4d897ae4ccbf4d5d1e9d060\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04ddc434742f2313e237bac0a66fcee1\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04ddc434742f2313e237bac0a66fcee1\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a141a1f7ed03f5747ee942445c6fcc7\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a141a1f7ed03f5747ee942445c6fcc7\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f38bfda3f7bcc90f86782212071775bd\transformed\jetified-fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f38bfda3f7bcc90f86782212071775bd\transformed\jetified-fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a9bebf8e5bbb464539d2f1c3751a881\transformed\jetified-activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a9bebf8e5bbb464539d2f1c3751a881\transformed\jetified-activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\14a3e82cceefba0de61ff34573e0919e\transformed\jetified-activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\14a3e82cceefba0de61ff34573e0919e\transformed\jetified-activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e6d558491d75611da77b86b8bf4ede4\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e6d558491d75611da77b86b8bf4ede4\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1161f0a8562187a0c56ef89266bbe2e\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1161f0a8562187a0c56ef89266bbe2e\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7eec3ffd9134b39a33221bcb756c708\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7eec3ffd9134b39a33221bcb756c708\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6e1ecf787ba6ce06fa3f517ea2d03c8\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6e1ecf787ba6ce06fa3f517ea2d03c8\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5c12ae7945d45ea077704eef99e3ed9\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5c12ae7945d45ea077704eef99e3ed9\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b260ca8e4027972f84f9b96089a2042e\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b260ca8e4027972f84f9b96089a2042e\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3163277d5a931e78681c2ed1ad88c00\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3163277d5a931e78681c2ed1ad88c00\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a74ef3bec649a4b6aa4b5971933d0b91\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a74ef3bec649a4b6aa4b5971933d0b91\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.palette:palette-ktx:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\166d0fbbb52470a485b7716ec68b28c4\transformed\jetified-palette-ktx-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.palette:palette-ktx:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\166d0fbbb52470a485b7716ec68b28c4\transformed\jetified-palette-ktx-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.palette:palette:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7e7e3b48202b6927c1f20fec059f7459\transformed\palette-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.palette:palette:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7e7e3b48202b6927c1f20fec059f7459\transformed\palette-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40aecd3cc962e8b264883814ae5a2fd0\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40aecd3cc962e8b264883814ae5a2fd0\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\906493b48761c290243a737adfc0eb3a\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\906493b48761c290243a737adfc0eb3a\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f58464ea0003e8effdc4bb405eefaa5\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f58464ea0003e8effdc4bb405eefaa5\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d7b75ca0408efcb2e6fb262d213dd35f\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d7b75ca0408efcb2e6fb262d213dd35f\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2467365d615c8f4d74985839be4e3a72\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2467365d615c8f4d74985839be4e3a72\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6c67302a26437d67690b4de1a6c0ac5\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6c67302a26437d67690b4de1a6c0ac5\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6b7dbe88d3a04c6d55aa7c581f20597\transformed\jetified-window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6b7dbe88d3a04c6d55aa7c581f20597\transformed\jetified-window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c108dd56627f30fe94755d1a2faeaf2\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c108dd56627f30fe94755d1a2faeaf2\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5c72dbf7ab217c6ae0efd6d2ecc3ef52\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5c72dbf7ab217c6ae0efd6d2ecc3ef52\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d219e3c7c24445de7d034f0ee27187bf\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d219e3c7c24445de7d034f0ee27187bf\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7362735739f227c043f1b1c8915d2b18\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7362735739f227c043f1b1c8915d2b18\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7115d9f9b930696a861e118f0dba3e64\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7115d9f9b930696a861e118f0dba3e64\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47b54bf287343bbce7eafca9f7c47b52\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47b54bf287343bbce7eafca9f7c47b52\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06ebe832fe25647990d118df0bb01aab\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06ebe832fe25647990d118df0bb01aab\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f2aa0fe1bbaeecf299c06bc7c5baadf\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f2aa0fe1bbaeecf299c06bc7c5baadf\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ee40adaae230d5d7ac7fb7ac3d13327\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ee40adaae230d5d7ac7fb7ac3d13327\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\391dcfbb653a3c36bffe25fdfdb14322\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\391dcfbb653a3c36bffe25fdfdb14322\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\16a9e14541efe46b4c93038dbbb0fa37\transformed\jetified-lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\16a9e14541efe46b4c93038dbbb0fa37\transformed\jetified-lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\add5756475d353f3d2526d69c987d8ce\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\add5756475d353f3d2526d69c987d8ce\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e301eb0149c4dcf3d8f0b2c16fc56c0\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e301eb0149c4dcf3d8f0b2c16fc56c0\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71d99d0694eeca94e20096f2777d039d\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71d99d0694eeca94e20096f2777d039d\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d552aa603d1087ef646d07674c8196c9\transformed\jetified-core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d552aa603d1087ef646d07674c8196c9\transformed\jetified-core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78f81a6d22eb336d8ab0e25f80a24035\transformed\jetified-annotation-experimental-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78f81a6d22eb336d8ab0e25f80a24035\transformed\jetified-annotation-experimental-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba34744bec8d4220a6118a6c2670b4a8\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba34744bec8d4220a6118a6c2670b4a8\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e6807f253dd82e862a5356e763b3b35\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e6807f253dd82e862a5356e763b3b35\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d18b3c4463a71c8eff89874c1db57286\transformed\jetified-gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d18b3c4463a71c8eff89874c1db57286\transformed\jetified-gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1a9b06bc28deabc21eb44e4ca38562\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1a9b06bc28deabc21eb44e4ca38562\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b0e68efff417ea48090e3b4aa9b30dda\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b0e68efff417ea48090e3b4aa9b30dda\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c19a4bdd040db89b795fe7f484312ec4\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c19a4bdd040db89b795fe7f484312ec4\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382da296a152b21823bfc19980aa0be4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382da296a152b21823bfc19980aa0be4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ff4466173ff565df36c2f41f0366bc7\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ff4466173ff565df36c2f41f0366bc7\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ca003ed9c217c662075a7d5b908039fd\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ca003ed9c217c662075a7d5b908039fd\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e39ca6cc1bda455f5d2c5feefe850ab\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e39ca6cc1bda455f5d2c5feefe850ab\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07c22a9090ac8adfcfa0bb4291cf058f\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07c22a9090ac8adfcfa0bb4291cf058f\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cba683e472751d3d4b17e60341ee8264\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cba683e472751d3d4b17e60341ee8264\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml
activity#com.karumi.dexter.DexterActivity
ADDED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b7043eedc50cada365136aa0e9ac9a0\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:27:9-29:72
	android:theme
		ADDED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b7043eedc50cada365136aa0e9ac9a0\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b7043eedc50cada365136aa0e9ac9a0\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:28:13-60
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7eec3ffd9134b39a33221bcb756c708\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47b54bf287343bbce7eafca9f7c47b52\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47b54bf287343bbce7eafca9f7c47b52\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b0e68efff417ea48090e3b4aa9b30dda\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b0e68efff417ea48090e3b4aa9b30dda\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7eec3ffd9134b39a33221bcb756c708\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7eec3ffd9134b39a33221bcb756c708\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7eec3ffd9134b39a33221bcb756c708\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7eec3ffd9134b39a33221bcb756c708\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7eec3ffd9134b39a33221bcb756c708\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7eec3ffd9134b39a33221bcb756c708\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7eec3ffd9134b39a33221bcb756c708\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6b7dbe88d3a04c6d55aa7c581f20597\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6b7dbe88d3a04c6d55aa7c581f20597\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6b7dbe88d3a04c6d55aa7c581f20597\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6b7dbe88d3a04c6d55aa7c581f20597\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6b7dbe88d3a04c6d55aa7c581f20597\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6b7dbe88d3a04c6d55aa7c581f20597\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c108dd56627f30fe94755d1a2faeaf2\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c108dd56627f30fe94755d1a2faeaf2\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c108dd56627f30fe94755d1a2faeaf2\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.musicplayer.pro.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c108dd56627f30fe94755d1a2faeaf2\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c108dd56627f30fe94755d1a2faeaf2\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c108dd56627f30fe94755d1a2faeaf2\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c108dd56627f30fe94755d1a2faeaf2\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c108dd56627f30fe94755d1a2faeaf2\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.musicplayer.pro.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c108dd56627f30fe94755d1a2faeaf2\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c108dd56627f30fe94755d1a2faeaf2\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47b54bf287343bbce7eafca9f7c47b52\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47b54bf287343bbce7eafca9f7c47b52\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47b54bf287343bbce7eafca9f7c47b52\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
