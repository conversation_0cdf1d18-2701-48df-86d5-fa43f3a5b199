<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="bottom_music_bar" modulePackage="com.musicplayer.pro" filePath="app\src\main\res\layout\bottom_music_bar.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/bottom_music_bar_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="145" endOffset="51"/></Target><Target id="@+id/miniProgressBar" view="ProgressBar"><Expressions/><location startLine="17" startOffset="4" endLine="28" endOffset="29"/></Target><Target id="@+id/miniAlbumCoverCard" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="31" startOffset="4" endLine="60" endOffset="39"/></Target><Target id="@+id/miniAlbumCover" view="ImageView"><Expressions/><location startLine="44" startOffset="8" endLine="50" endOffset="62"/></Target><Target id="@+id/playingIndicator" view="View"><Expressions/><location startLine="53" startOffset="8" endLine="58" endOffset="39"/></Target><Target id="@+id/songInfoLayout" view="LinearLayout"><Expressions/><location startLine="63" startOffset="4" endLine="126" endOffset="18"/></Target><Target id="@+id/miniSongTitle" view="TextView"><Expressions/><location startLine="76" startOffset="8" endLine="90" endOffset="66"/></Target><Target id="@+id/miniArtistName" view="TextView"><Expressions/><location startLine="98" startOffset="12" endLine="107" endOffset="45"/></Target><Target id="@+id/qualityIndicator" view="TextView"><Expressions/><location startLine="110" startOffset="12" endLine="122" endOffset="43"/></Target><Target id="@+id/btnMiniPlayPause" view="ImageButton"><Expressions/><location startLine="129" startOffset="4" endLine="143" endOffset="68"/></Target></Targets></Layout>