# تعليمات التشغيل السريع - Music Player Pro Kotlin

## 🚀 التشغيل السريع

### Windows:
```bash
# تشغيل سكريبت الإعداد التلقائي
setup_windows.bat
```

### Linux/Mac:
```bash
# جعل السكريبت قابل للتنفيذ
chmod +x setup_linux.sh

# تشغيل سكريبت الإعداد
./setup_linux.sh
```

### Python (جميع الأنظمة):
```bash
# تشغيل سكريبت Python المتقدم
python3 setup_and_run.py
```

## 📋 ما يحدث تلقائياً

### 1. فحص وتثبيت المتطلبات:
- ✅ **Java 11+** - تحميل وتثبيت OpenJDK
- ✅ **Android SDK** - تحميل Command Line Tools
- ✅ **Gradle 8.4** - تحميل وإعداد
- ✅ **Python 3.8+** - فحص الوجود
- ✅ **Git** - فحص الوجود

### 2. <PERSON><PERSON><PERSON><PERSON> Android SDK:
- 📦 تحميل Command Line Tools
- 📦 تثبيت Platform Tools
- 📦 تثبيت Android 34 & 33
- 📦 تثبيت Build Tools 34.0.0
- 📦 تثبيت NDK 25.2.9519653
- 📦 قبول جميع التراخيص

### 3. إنشاء ملفات المشروع:
- 📝 `gradle.properties` - إعدادات Gradle
- 📝 `settings.gradle` - إعدادات المشروع
- 📝 `build.gradle` - ملف البناء الجذر
- 📝 `local.properties` - مسارات SDK
- 📁 مجلدات المشروع والموارد

### 4. بناء المشروع:
- 🔧 إنشاء Gradle Wrapper
- 🧹 تنظيف المشروع
- 🏗️ بناء APK للتطوير
- 📱 إنشاء ملف APK جاهز للتثبيت

## ⏱️ الوقت المتوقع

| المرحلة | الوقت |
|---------|-------|
| تحميل المتطلبات | 5-10 دقائق |
| إعداد SDK | 3-5 دقائق |
| بناء المشروع | 5-15 دقيقة |
| **المجموع** | **15-30 دقيقة** |

## 📁 النتيجة النهائية

بعد اكتمال الإعداد ستجد:

```
kotlin_version/
├── 📱 app/build/outputs/apk/debug/app-debug.apk  # APK جاهز
├── 🛠️ android-sdk/                              # Android SDK
├── 🔧 tools/                                    # الأدوات المحملة
├── ⚙️ gradlew                                   # Gradle Wrapper
├── 📝 gradle.properties                         # إعدادات Gradle
├── 📝 settings.gradle                           # إعدادات المشروع
└── 📝 build.gradle                              # ملف البناء
```

## 🎯 الأوامر المفيدة بعد الإعداد

### بناء APK:
```bash
# APK للتطوير
./gradlew assembleDebug

# APK للإنتاج
./gradlew assembleRelease

# بناء كامل
./gradlew build
```

### تنظيف وصيانة:
```bash
# تنظيف المشروع
./gradlew clean

# تحديث التبعيات
./gradlew --refresh-dependencies

# فحص التبعيات
./gradlew dependencies
```

### اختبار وتحليل:
```bash
# تشغيل الاختبارات
./gradlew test

# تحليل الكود
./gradlew lint

# تقرير الأمان
./gradlew dependencyCheckAnalyze
```

## 🔧 حل المشاكل الشائعة

### مشكلة: Java غير موجود
```bash
# Windows
# تحميل من: https://adoptium.net/

# Linux
sudo apt install openjdk-11-jdk

# Mac
brew install openjdk@11
```

### مشكلة: Android SDK غير موجود
```bash
# إعادة تشغيل سكريبت الإعداد
./setup_linux.sh

# أو تحميل يدوي من:
# https://developer.android.com/studio#command-tools
```

### مشكلة: Gradle فشل
```bash
# تنظيف وإعادة البناء
./gradlew clean
./gradlew assembleDebug --refresh-dependencies

# أو إعادة إنشاء Wrapper
gradle wrapper --gradle-version 8.4
```

### مشكلة: نفاد مساحة القرص
```bash
# تنظيف ملفات البناء
./gradlew clean

# حذف ملفات مؤقتة
rm -rf ~/.gradle/caches/
rm -rf ~/.android/build-cache/
```

### مشكلة: بطء الإنترنت
```bash
# استخدام مرآة محلية (إذا متوفرة)
# أو تشغيل السكريبت في وقت أقل ازدحاماً
```

## 📱 تثبيت APK على الجهاز

### عبر ADB:
```bash
# تمكين USB Debugging على الجهاز أولاً
adb install app/build/outputs/apk/debug/app-debug.apk
```

### عبر نقل الملف:
1. انسخ `app-debug.apk` إلى الجهاز
2. فعّل "مصادر غير معروفة" في الإعدادات
3. اضغط على ملف APK لتثبيته

## 🎨 فتح في Android Studio

1. افتح Android Studio
2. اختر "Open an existing project"
3. حدد مجلد `kotlin_version`
4. انتظر تحميل التبعيات
5. اضغط "Run" لتشغيل التطبيق

## 🔄 تحديث المشروع

### تحديث Gradle:
```bash
./gradlew wrapper --gradle-version 8.5
```

### تحديث Android Gradle Plugin:
```bash
# في build.gradle
classpath 'com.android.tools.build:gradle:8.3.0'
```

### تحديث Kotlin:
```bash
# في build.gradle
ext.kotlin_version = "1.9.22"
```

## 📊 معلومات الأداء

### حجم APK المتوقع:
- **Debug**: 25-40 MB
- **Release**: 15-25 MB (مع ProGuard)

### متطلبات النظام:
- **RAM**: 4GB+ للتطوير
- **Storage**: 10GB+ للأدوات
- **Android**: 5.0+ (API 21)

## 🆘 الحصول على المساعدة

### إذا واجهت مشاكل:

1. **تحقق من السجلات**:
   ```bash
   ./gradlew assembleDebug --info
   ```

2. **نظف وأعد البناء**:
   ```bash
   ./gradlew clean assembleDebug
   ```

3. **تحقق من متغيرات البيئة**:
   ```bash
   echo $ANDROID_HOME
   echo $JAVA_HOME
   ```

4. **أعد تشغيل السكريبت**:
   ```bash
   ./setup_linux.sh
   ```

## ✅ علامات النجاح

عند نجاح الإعداد ستشاهد:

```
✅ Java موجود
✅ تم إعداد Android SDK  
✅ تم إعداد Gradle
✅ تم إنشاء ملفات المشروع
✅ تم بناء المشروع بنجاح!
📱 تم إنشاء APK: app/build/outputs/apk/debug/app-debug.apk
```

---

**🎉 مبروك! مشروع Music Player Pro جاهز للتطوير والاستخدام!**
