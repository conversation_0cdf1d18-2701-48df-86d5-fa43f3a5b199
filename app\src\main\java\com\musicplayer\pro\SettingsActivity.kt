package com.musicplayer.pro

import android.content.Intent
import android.content.SharedPreferences
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.provider.Settings
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.app.AppCompatDelegate
import androidx.core.content.ContextCompat
import androidx.preference.ListPreference
import androidx.preference.Preference
import androidx.preference.PreferenceFragmentCompat
import androidx.preference.PreferenceManager
import androidx.preference.SwitchPreferenceCompat
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.launch
import com.musicplayer.pro.managers.AudioEffectsManager
import com.musicplayer.pro.managers.BackupManager
import com.musicplayer.pro.managers.CacheManager

/**
 * نشاط الإعدادات - مطابق لشاشة الإعدادات في Python
 */
class SettingsActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_settings)

        // إعداد شريط الأدوات
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            title = getString(R.string.settings)
        }

        // عرض fragment الإعدادات
        if (savedInstanceState == null) {
            supportFragmentManager
                .beginTransaction()
                .replace(R.id.settings_container, SettingsFragment())
                .commit()
        }
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }

    /**
     * Fragment الإعدادات الرئيسي
     */
    class SettingsFragment : PreferenceFragmentCompat(), SharedPreferences.OnSharedPreferenceChangeListener {

        private lateinit var audioEffectsManager: AudioEffectsManager
        private lateinit var backupManager: BackupManager
        private lateinit var cacheManager: CacheManager

        override fun onCreatePreferences(savedInstanceState: Bundle?, rootKey: String?) {
            setPreferencesFromResource(R.xml.preferences, rootKey)

            // تهيئة المدراء
            audioEffectsManager = AudioEffectsManager(requireContext())
            backupManager = BackupManager(requireContext())
            cacheManager = CacheManager(requireContext())

            setupPreferences()
        }

        override fun onResume() {
            super.onResume()
            preferenceManager.sharedPreferences?.registerOnSharedPreferenceChangeListener(this)
        }

        override fun onPause() {
            super.onPause()
            preferenceManager.sharedPreferences?.unregisterOnSharedPreferenceChangeListener(this)
        }

        /**
         * إعداد التفضيلات
         */
        private fun setupPreferences() {
            setupThemePreferences()
            setupAudioPreferences()
            setupDownloadPreferences()
            setupStoragePreferences()
            setupAboutPreferences()
        }

        /**
         * إعداد تفضيلات الثيم
         */
        private fun setupThemePreferences() {
            // تفضيل الثيم
            findPreference<ListPreference>("theme_preference")?.apply {
                setOnPreferenceChangeListener { _, newValue ->
                    applyTheme(newValue.toString())
                    true
                }
            }

            // تفضيل اللغة
            findPreference<ListPreference>("language_preference")?.apply {
                setOnPreferenceChangeListener { _, newValue ->
                    changeLanguage(newValue.toString())
                    true
                }
            }
        }

        /**
         * إعداد تفضيلات الصوت
         */
        private fun setupAudioPreferences() {
            // جودة الصوت
            findPreference<ListPreference>("audio_quality_preference")?.apply {
                setOnPreferenceChangeListener { _, newValue ->
                    audioEffectsManager.setAudioQuality(newValue.toString())
                    true
                }
            }

            // المعادل الصوتي
            findPreference<Preference>("equalizer_preference")?.apply {
                setOnPreferenceClickListener {
                    openEqualizerSettings()
                    true
                }
            }

            // تأثيرات الصوت
            findPreference<SwitchPreferenceCompat>("audio_effects_preference")?.apply {
                setOnPreferenceChangeListener { _, newValue ->
                    audioEffectsManager.setAudioEffectsEnabled(newValue as Boolean)
                    true
                }
            }

            // تحسين الجهير
            findPreference<SwitchPreferenceCompat>("bass_boost_preference")?.apply {
                setOnPreferenceChangeListener { _, newValue ->
                    audioEffectsManager.setBassBoostEnabled(newValue as Boolean)
                    true
                }
            }

            // تحسين الصوت المحيطي
            findPreference<SwitchPreferenceCompat>("virtualizer_preference")?.apply {
                setOnPreferenceChangeListener { _, newValue ->
                    audioEffectsManager.setVirtualizerEnabled(newValue as Boolean)
                    true
                }
            }
        }

        /**
         * إعداد تفضيلات التحميل
         */
        private fun setupDownloadPreferences() {
            // مجلد التحميل
            findPreference<Preference>("download_folder_preference")?.apply {
                setOnPreferenceClickListener {
                    selectDownloadFolder()
                    true
                }
            }

            // جودة التحميل الافتراضية
            findPreference<ListPreference>("default_download_quality")?.apply {
                setOnPreferenceChangeListener { _, newValue ->
                    // حفظ الجودة الافتراضية
                    true
                }
            }

            // تنسيق التحميل الافتراضي
            findPreference<ListPreference>("default_download_format")?.apply {
                setOnPreferenceChangeListener { _, newValue ->
                    // حفظ التنسيق الافتراضي
                    true
                }
            }

            // التحميل التلقائي للصور المصغرة
            findPreference<SwitchPreferenceCompat>("auto_download_thumbnails")?.apply {
                setOnPreferenceChangeListener { _, newValue ->
                    // تفعيل/إلغاء التحميل التلقائي
                    true
                }
            }

            // التحميل عبر WiFi فقط
            findPreference<SwitchPreferenceCompat>("wifi_only_downloads")?.apply {
                setOnPreferenceChangeListener { _, newValue ->
                    // تفعيل/إلغاء التحميل عبر WiFi فقط
                    true
                }
            }
        }

        /**
         * إعداد تفضيلات التخزين
         */
        private fun setupStoragePreferences() {
            // مسح الذاكرة المؤقتة
            findPreference<Preference>("clear_cache_preference")?.apply {
                setOnPreferenceClickListener {
                    showClearCacheDialog()
                    true
                }
            }

            // نسخ احتياطي للبيانات
            findPreference<Preference>("backup_data_preference")?.apply {
                setOnPreferenceClickListener {
                    createBackup()
                    true
                }
            }

            // استعادة البيانات
            findPreference<Preference>("restore_data_preference")?.apply {
                setOnPreferenceClickListener {
                    restoreBackup()
                    true
                }
            }

            // إعادة تعيين الإعدادات
            findPreference<Preference>("reset_settings_preference")?.apply {
                setOnPreferenceClickListener {
                    showResetSettingsDialog()
                    true
                }
            }
        }

        /**
         * إعداد تفضيلات حول التطبيق
         */
        private fun setupAboutPreferences() {
            // معلومات التطبيق
            findPreference<Preference>("app_info_preference")?.apply {
                summary = "${getString(R.string.version)} ${getAppVersion()}"
                setOnPreferenceClickListener {
                    showAppInfoDialog()
                    true
                }
            }

            // التحقق من التحديثات
            findPreference<Preference>("check_updates_preference")?.apply {
                setOnPreferenceClickListener {
                    checkForUpdates()
                    true
                }
            }

            // تقييم التطبيق
            findPreference<Preference>("rate_app_preference")?.apply {
                setOnPreferenceClickListener {
                    rateApp()
                    true
                }
            }

            // مشاركة التطبيق
            findPreference<Preference>("share_app_preference")?.apply {
                setOnPreferenceClickListener {
                    shareApp()
                    true
                }
            }

            // الترخيص
            findPreference<Preference>("license_preference")?.apply {
                setOnPreferenceClickListener {
                    showLicenseDialog()
                    true
                }
            }
        }

        override fun onSharedPreferenceChanged(sharedPreferences: SharedPreferences?, key: String?) {
            when (key) {
                "theme_preference" -> {
                    // إعادة تطبيق الثيم
                    activity?.recreate()
                }
                "language_preference" -> {
                    // إعادة تشغيل التطبيق لتطبيق اللغة
                    activity?.recreate()
                }
            }
        }

        /**
         * تطبيق الثيم
         */
        private fun applyTheme(theme: String) {
            when (theme) {
                "light" -> AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO)
                "dark" -> AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES)
                "auto" -> AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_FOLLOW_SYSTEM)
            }
        }

        /**
         * تغيير اللغة
         */
        private fun changeLanguage(language: String) {
            // منطق تغيير اللغة
            // يمكن تنفيذه لاحقاً
        }

        /**
         * فتح إعدادات المعادل الصوتي
         */
        private fun openEqualizerSettings() {
            // فحص إذن تسجيل الصوت أولاً
            if (ContextCompat.checkSelfPermission(requireContext(), android.Manifest.permission.RECORD_AUDIO)
                != PackageManager.PERMISSION_GRANTED) {

                showOptionalPermissionDialog(
                    android.Manifest.permission.RECORD_AUDIO,
                    "إذن تسجيل الصوت",
                    "يحتاج المعادل الصوتي إلى إذن تسجيل الصوت للعمل بشكل صحيح."
                )
                return
            }

            try {
                val intent = Intent(Intent.ACTION_MAIN)
                intent.setClassName("com.android.musicfx", "com.android.musicfx.ControlPanelPicker")
                startActivity(intent)
            } catch (e: Exception) {
                // فتح إعدادات المعادل المخصص
                audioEffectsManager.openCustomEqualizer()
            }
        }

        /**
         * اختيار مجلد التحميل
         */
        private fun selectDownloadFolder() {
            val intent = Intent(Intent.ACTION_OPEN_DOCUMENT_TREE)
            startActivityForResult(intent, 1001)
        }

        /**
         * عرض dialog مسح الذاكرة المؤقتة
         */
        private fun showClearCacheDialog() {
            val cacheSize = cacheManager.getCacheSize()
            
            AlertDialog.Builder(requireContext())
                .setTitle(getString(R.string.clear_cache))
                .setMessage("حجم الذاكرة المؤقتة: $cacheSize\nهل تريد مسحها؟")
                .setPositiveButton(getString(R.string.yes)) { _, _ ->
                    cacheManager.clearCache()
                    android.widget.Toast.makeText(requireContext(), "تم مسح الذاكرة المؤقتة", android.widget.Toast.LENGTH_SHORT).show()
                }
                .setNegativeButton(getString(R.string.no), null)
                .show()
        }

        /**
         * إنشاء نسخة احتياطية
         */
        private fun createBackup() {
            lifecycleScope.launch {
                backupManager.createBackup { success ->
                    val message = if (success) "تم إنشاء النسخة الاحتياطية بنجاح" else "فشل في إنشاء النسخة الاحتياطية"
                    android.widget.Toast.makeText(requireContext(), message, android.widget.Toast.LENGTH_SHORT).show()
                }
            }
        }

        /**
         * استعادة النسخة الاحتياطية
         */
        private fun restoreBackup() {
            lifecycleScope.launch {
                backupManager.restoreBackup { success ->
                    val message = if (success) "تم استعادة النسخة الاحتياطية بنجاح" else "فشل في استعادة النسخة الاحتياطية"
                    android.widget.Toast.makeText(requireContext(), message, android.widget.Toast.LENGTH_SHORT).show()
                }
            }
        }

        /**
         * عرض dialog إعادة تعيين الإعدادات
         */
        private fun showResetSettingsDialog() {
            AlertDialog.Builder(requireContext())
                .setTitle(getString(R.string.reset_settings))
                .setMessage("هل تريد إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟")
                .setPositiveButton(getString(R.string.yes)) { _, _ ->
                    resetSettings()
                }
                .setNegativeButton(getString(R.string.no), null)
                .show()
        }

        /**
         * إعادة تعيين الإعدادات
         */
        private fun resetSettings() {
            PreferenceManager.getDefaultSharedPreferences(requireContext())
                .edit()
                .clear()
                .apply()
            
            android.widget.Toast.makeText(requireContext(), "تم إعادة تعيين الإعدادات", android.widget.Toast.LENGTH_SHORT).show()
            activity?.recreate()
        }

        /**
         * عرض معلومات التطبيق
         */
        private fun showAppInfoDialog() {
            val message = """
                الاسم: ${getString(R.string.app_name)}
                الإصدار: ${getAppVersion()}
                رقم البناء: ${getAppVersionCode()}

                تطبيق مشغل موسيقى متقدم مع ميزات التحميل والتأثيرات الصوتية.
            """.trimIndent()

            AlertDialog.Builder(requireContext())
                .setTitle(getString(R.string.app_info))
                .setMessage(message)
                .setIcon(R.drawable.ic_music_note)
                .setPositiveButton(getString(R.string.ok), null)
                .show()
        }

        /**
         * التحقق من التحديثات
         */
        private fun checkForUpdates() {
            // منطق التحقق من التحديثات
            android.widget.Toast.makeText(requireContext(), "لا توجد تحديثات متاحة", android.widget.Toast.LENGTH_SHORT).show()
        }

        /**
         * تقييم التطبيق
         */
        private fun rateApp() {
            try {
                val intent = Intent(Intent.ACTION_VIEW, Uri.parse("market://details?id=${requireContext().packageName}"))
                startActivity(intent)
            } catch (e: Exception) {
                val intent = Intent(Intent.ACTION_VIEW, Uri.parse("https://play.google.com/store/apps/details?id=${requireContext().packageName}"))
                startActivity(intent)
            }
        }

        /**
         * مشاركة التطبيق
         */
        private fun shareApp() {
            val shareText = "جرب تطبيق ${getString(R.string.app_name)} - مشغل موسيقى متقدم!\nhttps://play.google.com/store/apps/details?id=${requireContext().packageName}"
            
            val intent = Intent(Intent.ACTION_SEND).apply {
                type = "text/plain"
                putExtra(Intent.EXTRA_TEXT, shareText)
            }
            
            startActivity(Intent.createChooser(intent, "مشاركة التطبيق"))
        }

        /**
         * عرض الترخيص
         */
        private fun showLicenseDialog() {
            AlertDialog.Builder(requireContext())
                .setTitle(getString(R.string.license))
                .setMessage(getString(R.string.license_text))
                .setPositiveButton(getString(R.string.ok), null)
                .show()
        }

        /**
         * الحصول على إصدار التطبيق
         */
        private fun getAppVersion(): String {
            return try {
                val packageInfo = requireContext().packageManager.getPackageInfo(requireContext().packageName, 0)
                packageInfo.versionName ?: "1.0.0"
            } catch (e: Exception) {
                "1.0.0"
            }
        }

        /**
         * الحصول على رقم إصدار التطبيق
         */
        private fun getAppVersionCode(): String {
            return try {
                val packageInfo = requireContext().packageManager.getPackageInfo(requireContext().packageName, 0)
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.P) {
                    packageInfo.longVersionCode.toString()
                } else {
                    @Suppress("DEPRECATION")
                    packageInfo.versionCode.toString()
                }
            } catch (e: Exception) {
                "1"
            }
        }

        /**
         * عرض dialog للأذونات الاختيارية
         */
        private fun showOptionalPermissionDialog(permission: String, title: String, message: String) {
            AlertDialog.Builder(requireContext())
                .setTitle(title)
                .setMessage("$message\n\nهل تريد منح هذا الإذن الآن؟")
                .setIcon(R.drawable.ic_info)
                .setPositiveButton("منح الإذن") { _, _ ->
                    requestOptionalPermission(permission)
                }
                .setNegativeButton("لاحقاً", null)
                .show()
        }

        /**
         * طلب إذن اختياري
         */
        private fun requestOptionalPermission(permission: String) {
            try {
                val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                intent.data = Uri.fromParts("package", requireContext().packageName, null)
                startActivity(intent)
            } catch (e: Exception) {
                android.widget.Toast.makeText(requireContext(), "يرجى منح الإذن من إعدادات الجهاز", android.widget.Toast.LENGTH_LONG).show()
            }
        }
    }
}
