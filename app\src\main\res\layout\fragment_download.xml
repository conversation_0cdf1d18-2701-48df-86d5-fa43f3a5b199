<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary">

    <!-- قائمة التحميلات -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewDownloads"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipToPadding="false"
        android:paddingBottom="16dp"
        tools:listitem="@layout/item_download" />

    <!-- رسالة عدم وجود تحميلات -->
    <LinearLayout
        android:id="@+id/emptyStateLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        tools:visibility="visible">

        <ImageView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:alpha="0.5"
            android:src="@drawable/ic_download"
            android:tint="@color/text_secondary" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="@string/no_downloads"
            android:textColor="@color/text_secondary"
            android:textSize="18sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="أضف رابط لبدء التحميل"
            android:textColor="@color/text_tertiary"
            android:textSize="14sp" />

    </LinearLayout>

    <!-- زر إضافة تحميل -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabAddDownload"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:contentDescription="@string/add_download"
        app:srcCompat="@drawable/ic_add"
        app:backgroundTint="@color/accent_primary"
        app:tint="@color/white" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
