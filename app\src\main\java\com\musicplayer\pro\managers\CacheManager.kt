package com.musicplayer.pro.managers

import android.content.Context
import java.io.File
import kotlin.math.pow

/**
 * مدير الذاكرة المؤقتة - مطابق لإدارة الذاكرة المؤقتة في Python
 */
class CacheManager(private val context: Context) {
    
    private val cacheDir = context.cacheDir
    private val externalCacheDir = context.externalCacheDir
    
    /**
     * الحصول على حجم الذاكرة المؤقتة
     */
    fun getCacheSize(): String {
        val internalCacheSize = calculateDirectorySize(cacheDir)
        val externalCacheSize = externalCacheDir?.let { calculateDirectorySize(it) } ?: 0L
        val totalSize = internalCacheSize + externalCacheSize
        
        return formatFileSize(totalSize)
    }
    
    /**
     * مسح الذاكرة المؤقتة
     */
    fun clearCache(): Boolean {
        return try {
            var success = true
            
            // مسح الذاكرة المؤقتة الداخلية
            success = success && clearDirectory(cacheDir)
            
            // مسح الذاكرة المؤقتة الخارجية
            externalCacheDir?.let { dir ->
                success = success && clearDirectory(dir)
            }
            
            success
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }
    
    /**
     * مسح ذاكرة مؤقتة محددة
     */
    fun clearSpecificCache(cacheType: CacheType): Boolean {
        return try {
            when (cacheType) {
                CacheType.IMAGES -> clearImageCache()
                CacheType.AUDIO -> clearAudioCache()
                CacheType.DOWNLOADS -> clearDownloadCache()
                CacheType.THUMBNAILS -> clearThumbnailCache()
            }
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }
    
    /**
     * مسح ذاكرة الصور المؤقتة
     */
    private fun clearImageCache(): Boolean {
        val imagesCacheDir = File(cacheDir, "images")
        return if (imagesCacheDir.exists()) {
            clearDirectory(imagesCacheDir)
        } else {
            true
        }
    }
    
    /**
     * مسح ذاكرة الصوت المؤقتة
     */
    private fun clearAudioCache(): Boolean {
        val audioCacheDir = File(cacheDir, "audio")
        return if (audioCacheDir.exists()) {
            clearDirectory(audioCacheDir)
        } else {
            true
        }
    }
    
    /**
     * مسح ذاكرة التحميلات المؤقتة
     */
    private fun clearDownloadCache(): Boolean {
        val downloadCacheDir = File(cacheDir, "downloads")
        return if (downloadCacheDir.exists()) {
            clearDirectory(downloadCacheDir)
        } else {
            true
        }
    }
    
    /**
     * مسح ذاكرة الصور المصغرة المؤقتة
     */
    private fun clearThumbnailCache(): Boolean {
        val thumbnailCacheDir = File(cacheDir, "thumbnails")
        return if (thumbnailCacheDir.exists()) {
            clearDirectory(thumbnailCacheDir)
        } else {
            true
        }
    }
    
    /**
     * حساب حجم مجلد
     */
    private fun calculateDirectorySize(directory: File): Long {
        var size = 0L
        
        if (directory.exists() && directory.isDirectory) {
            directory.listFiles()?.forEach { file ->
                size += if (file.isDirectory) {
                    calculateDirectorySize(file)
                } else {
                    file.length()
                }
            }
        }
        
        return size
    }
    
    /**
     * مسح مجلد
     */
    private fun clearDirectory(directory: File): Boolean {
        return try {
            if (directory.exists() && directory.isDirectory) {
                directory.listFiles()?.forEach { file ->
                    if (file.isDirectory) {
                        clearDirectory(file)
                    }
                    file.delete()
                }
            }
            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }
    
    /**
     * تنسيق حجم الملف
     */
    private fun formatFileSize(size: Long): String {
        if (size <= 0) return "0 B"
        
        val units = arrayOf("B", "KB", "MB", "GB", "TB")
        val digitGroups = (Math.log10(size.toDouble()) / Math.log10(1024.0)).toInt()
        
        return String.format(
            "%.1f %s",
            size / 1024.0.pow(digitGroups.toDouble()),
            units[digitGroups]
        )
    }
    
    /**
     * الحصول على تفاصيل الذاكرة المؤقتة
     */
    fun getCacheDetails(): CacheDetails {
        val internalCacheSize = calculateDirectorySize(cacheDir)
        val externalCacheSize = externalCacheDir?.let { calculateDirectorySize(it) } ?: 0L
        
        val imagesCacheSize = calculateDirectorySize(File(cacheDir, "images"))
        val audioCacheSize = calculateDirectorySize(File(cacheDir, "audio"))
        val downloadCacheSize = calculateDirectorySize(File(cacheDir, "downloads"))
        val thumbnailCacheSize = calculateDirectorySize(File(cacheDir, "thumbnails"))
        
        return CacheDetails(
            totalSize = internalCacheSize + externalCacheSize,
            internalCacheSize = internalCacheSize,
            externalCacheSize = externalCacheSize,
            imagesCacheSize = imagesCacheSize,
            audioCacheSize = audioCacheSize,
            downloadCacheSize = downloadCacheSize,
            thumbnailCacheSize = thumbnailCacheSize
        )
    }
    
    /**
     * تنظيف الذاكرة المؤقتة القديمة
     */
    fun cleanOldCache(maxAgeInDays: Int = 7): Boolean {
        return try {
            val maxAge = System.currentTimeMillis() - (maxAgeInDays * 24 * 60 * 60 * 1000L)
            
            cleanOldFilesInDirectory(cacheDir, maxAge)
            externalCacheDir?.let { cleanOldFilesInDirectory(it, maxAge) }
            
            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }
    
    /**
     * تنظيف الملفات القديمة في مجلد
     */
    private fun cleanOldFilesInDirectory(directory: File, maxAge: Long) {
        if (directory.exists() && directory.isDirectory) {
            directory.listFiles()?.forEach { file ->
                if (file.isDirectory) {
                    cleanOldFilesInDirectory(file, maxAge)
                } else if (file.lastModified() < maxAge) {
                    file.delete()
                }
            }
        }
    }
}

/**
 * أنواع الذاكرة المؤقتة
 */
enum class CacheType {
    IMAGES,
    AUDIO,
    DOWNLOADS,
    THUMBNAILS
}

/**
 * تفاصيل الذاكرة المؤقتة
 */
data class CacheDetails(
    val totalSize: Long,
    val internalCacheSize: Long,
    val externalCacheSize: Long,
    val imagesCacheSize: Long,
    val audioCacheSize: Long,
    val downloadCacheSize: Long,
    val thumbnailCacheSize: Long
)
