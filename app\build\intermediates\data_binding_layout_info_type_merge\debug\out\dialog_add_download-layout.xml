<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_add_download" modulePackage="com.musicplayer.pro" filePath="app\src\main\res\layout\dialog_add_download.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/dialog_add_download_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="122" endOffset="51"/></Target><Target id="@+id/dialogTitle" view="TextView"><Expressions/><location startLine="9" startOffset="4" endLine="20" endOffset="51"/></Target><Target id="@+id/urlInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="23" startOffset="4" endLine="43" endOffset="59"/></Target><Target id="@+id/urlEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="36" startOffset="8" endLine="41" endOffset="34"/></Target><Target id="@+id/qualityLabel" view="TextView"><Expressions/><location startLine="46" startOffset="4" endLine="56" endOffset="67"/></Target><Target id="@+id/qualitySpinner" view="Spinner"><Expressions/><location startLine="58" startOffset="4" endLine="66" endOffset="65"/></Target><Target id="@+id/formatLabel" view="TextView"><Expressions/><location startLine="69" startOffset="4" endLine="79" endOffset="67"/></Target><Target id="@+id/formatSpinner" view="Spinner"><Expressions/><location startLine="81" startOffset="4" endLine="89" endOffset="64"/></Target><Target id="@+id/cancelButton" view="Button"><Expressions/><location startLine="102" startOffset="8" endLine="109" endOffset="55"/></Target><Target id="@+id/downloadButton" view="Button"><Expressions/><location startLine="111" startOffset="8" endLine="118" endOffset="46"/></Target></Targets></Layout>